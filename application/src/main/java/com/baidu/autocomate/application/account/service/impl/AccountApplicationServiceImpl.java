package com.baidu.autocomate.application.account.service.impl;

import com.baidu.autocomate.application.account.cqe.BindLoginAccountCommand;
import com.baidu.autocomate.application.account.cqe.CreateUnLoginAccountCommand;
import com.baidu.autocomate.application.account.service.AccountApplicationService;
import com.baidu.autocomate.domain.account.entity.WebAccountEntity;
import com.baidu.autocomate.domain.account.enums.UserAccountType;
import com.baidu.autocomate.domain.account.repos.AccountRepository;
import com.baidu.autocomate.domain.agent.repos.AgentRepository;
import com.baidu.autocomate.domain.conversation.repos.ConversationRepository;
import com.baidu.autocomate.domain.jobs.repos.JobBuildRepository;
import com.baidu.autocomate.domain.llm.repos.LLMMessageRepository;
import com.baidu.autocomate.domain.llm.repos.LLMSessionRepository;
import com.baidu.autocomate.domain.message.repos.MessageRepository;
import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.repos.QuotaRepository;
import com.baidu.autocomate.domain.workflow.repos.WorkflowBuildRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@Slf4j
public class AccountApplicationServiceImpl implements AccountApplicationService {

    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private AgentRepository agentRepository;
    @Autowired
    private ConversationRepository conversationRepository;
    @Autowired
    private JobBuildRepository jobBuildRepository;
    @Autowired
    private LLMMessageRepository llmMessageRepository;
    @Autowired
    private LLMSessionRepository llmSessionRepository;
    @Autowired
    private MessageRepository messageRepository;
    @Autowired
    private WorkflowBuildRepository workflowBuildRepository;
    @Autowired
    private QuotaRepository quotaRepository;

    @Override
    public WebAccountEntity createUnLoginUserAccount(CreateUnLoginAccountCommand command) {
        return accountRepository.createUnLoginAccount();
    }

    @Override
    public WebAccountEntity findUnLoginUserAccount(String uniqueIdentifier) {
        return accountRepository.getValidUnLoginUserByIdentifier(uniqueIdentifier);
    }

    @Override
    public UserAccountType getUserAccountType(String uniqueIdentifier) {
        if (StringUtils.isBlank(uniqueIdentifier)) {
            return UserAccountType.OTHER;
        }
        return accountRepository.getUserAccountType(uniqueIdentifier);
    }

    @Override
    public void bindUnLoginAccountToLicenceAccount(BindLoginAccountCommand command) {
        log.info("start bindUnLoginAccountToLoginAccount: {}", command);
        String unLoginAccount = command.getUnLoginAccount();
        String loginAccount = command.getLoginAccount();
        UserAccountType unLoginUserType = getUserAccountType(unLoginAccount);
        UserAccountType loginUserType = getUserAccountType(loginAccount);
        Assert.isTrue(UserAccountType.UN_LOGIN.equals(unLoginUserType), "unLoginUser identifier not exist");
        Assert.isTrue(UserAccountType.LICENCE.equals(loginUserType), "loginUser licence not exist");
        agentRepository.replaceUserForAgent(unLoginAccount, loginAccount);
        conversationRepository.replaceUserForConversation(unLoginAccount, loginAccount);
        jobBuildRepository.replaceUserForJobBuild(unLoginAccount, loginAccount);
        llmMessageRepository.replaceUserForLLMMessage(unLoginAccount, loginAccount);
        llmSessionRepository.replaceUserForLLMSession(unLoginAccount, loginAccount);
        messageRepository.replaceUserForMessage(unLoginAccount, loginAccount);
        workflowBuildRepository.replaceUserForWorkflowBuild(unLoginAccount, loginAccount);
        accountRepository.bindUnLoginAccountToLoginAccount(unLoginAccount, loginAccount, command.getSource());
        quotaRepository.bindUnLoginAccountToLoginAccount(unLoginAccount, loginAccount, ResourceType.COMATE_WEB);
        log.info("end bindUnLoginAccountToLoginAccount: {}", command);
    }
}