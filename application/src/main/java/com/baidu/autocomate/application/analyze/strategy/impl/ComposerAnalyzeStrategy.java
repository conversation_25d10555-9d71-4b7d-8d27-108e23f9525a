package com.baidu.autocomate.application.analyze.strategy.impl;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.application.analyze.strategy.composer.ComposerQuotaChecker;
import com.baidu.autocomate.application.analyze.strategy.composer.ComposerSecurityAnalyzer;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.config.enums.ModelPlatformType;
import com.baidu.autocomate.domain.config.service.ModelConfigService;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ComposerAnalyzeStrategy extends DefaultAnalyzeStrategy implements AnalyzeStrategy {

    @Autowired
    private ComposerQuotaChecker composerQuotaChecker;

    @Autowired
    private ComposerSecurityAnalyzer composerSecurityAnalyzer;

    @Autowired
    private ModelConfigService modelConfigService;

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Override
    public String getSlash() {
        return Slash.COMPOSER.getName();
    }

    @Override
    public AnalyzeResult analyze(AnalyzeCommand analyzeCommand) {
        AnalyzeResult analyzeResult = new AnalyzeResult();

        ModelPlatformType modelPlatformType = modelConfigService.getModelPlatformType(analyzeCommand.getModelKey());
        // 如果用户使用了自己的代理模型，那么就没必要进行配额检查和安全分析了
        if (!ModelPlatformType.PROXY_MODEL.equals(modelPlatformType)) {
            performModelChecks(analyzeCommand, analyzeResult);
        }

        if (analyzeCommand.isIntent()) {
            handleIntent(analyzeCommand, analyzeResult);
        }
        return analyzeResult;
    }

    /**
     * 执行模型检查
     *
     * @param analyzeCommand 分析命令对象
     * @param analyzeResult  分析结果对象
     */
    private void performModelChecks(AnalyzeCommand analyzeCommand, AnalyzeResult analyzeResult) {
        // 检查并记录用户使用次数
        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);

        // 从 Redis 中读取配置，判断是否只使用 Claude 模型
        String useClaudeOnly = redisTemplate.opsForValue().get("composer_use_default_llm_only");
        if (!"true".equalsIgnoreCase(useClaudeOnly)) {
            // 分析用户是否有钓鱼/攻击行为
            long startTime = System.currentTimeMillis();
            composerSecurityAnalyzer.analyzeCodeSecurity(analyzeCommand, analyzeResult);
            log.info("Composer analyzeCodeSecurity cost: {} ms", System.currentTimeMillis() - startTime);
        }
    }

}
