/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.factory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.baidu.autocomate.application.adopt.strategy.AdoptStrategy;

import lombok.NonNull;

public class AdoptStrategyFactory {
    private static final Map<String, AdoptStrategy> ADOPT_STRATEGY_MAP = new ConcurrentHashMap<>();

    /**
     * 注册采纳策略
     *
     * @param slash
     * @return
     */
    public static void registerAdoptStrategy(@NonNull String slash, AdoptStrategy adoptStrategy) {
        ADOPT_STRATEGY_MAP.put(slash, adoptStrategy);
    }

    /**
     * 根据传入的字符串参数获取分析策略
     *
     * @param slash 字符串参数，表示要获取的策略
     * @return 返回对应的策略，如果参数为空或不存在对应的策略，则返回null
     */
    public static AdoptStrategy getAdoptStrategy(String slash) {
        AdoptStrategy adoptStrategy = ADOPT_STRATEGY_MAP.get(slash);
        return adoptStrategy;
    }
}
