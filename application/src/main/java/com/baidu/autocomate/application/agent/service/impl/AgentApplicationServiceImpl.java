package com.baidu.autocomate.application.agent.service.impl;

import com.baidu.autocomate.application.agent.bean.AgentAllConf;
import com.baidu.autocomate.application.agent.bean.CreateAgentCommand;
import com.baidu.autocomate.application.agent.bean.UpdateAgentCommand;
import com.baidu.autocomate.application.agent.service.AgentApplicationService;
import com.baidu.autocomate.application.workflow.bean.CreateJobCommand;
import com.baidu.autocomate.application.workflow.bean.CreateWorkflowCommand;
import com.baidu.autocomate.domain.agent.entity.AgentEntity;
import com.baidu.autocomate.domain.agent.entity.AgentIntentEntity;
import com.baidu.autocomate.domain.agent.entity.AgentSlashEntity;
import com.baidu.autocomate.domain.agent.repos.AgentRepository;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.prompts.PromptTemplate;
import com.baidu.autocomate.domain.jobs.aggregate.conf.JobConfEntity;
import com.baidu.autocomate.domain.jobs.aggregate.conf.JobConfEntity.JobInput;
import com.baidu.autocomate.domain.jobs.repos.JobConfRepository;
import com.baidu.autocomate.domain.tools.ToolBox;
import com.baidu.autocomate.domain.tools.enums.ToolType;
import com.baidu.autocomate.domain.workflow.conf.WorkflowConfEntity;
import com.baidu.autocomate.domain.workflow.repos.WorkflowConfRepository;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class AgentApplicationServiceImpl implements AgentApplicationService {

    private static final Type INPUT_TYPE = new TypeToken<List<JobInput>>(){}.getType();

    private static final Type OUTPUT_TYPE = new TypeToken<List<JobConfEntity.JobOutput>>(){}.getType();

    @Autowired
    private AgentRepository agentRepository;

    @Autowired
    private WorkflowConfRepository workflowConfRepository;

    @Autowired
    private JobConfRepository jobConfRepository;

    @Override
    public AgentAllConf create(CreateAgentCommand command) {
        // 1、创建Agent
        AgentEntity agent = AgentEntity.builder()
                .name(command.getName())
                .description(command.getDescription())
                .build();
        agentRepository.saveOrUpdateAgent(agent);

        // 2、创建Workflow、Job，记录Slash和Intent数据
        List<WorkflowConfEntity> workflows = new ArrayList<>();
        List<AgentIntentEntity> intents = new ArrayList<>();
        List<AgentSlashEntity> slashes = new ArrayList<>();

        for (CreateWorkflowCommand workflowCommand : command.getWorkflows()) {
            WorkflowConfEntity workflow = createWorkflow(workflowCommand, agent);
            List<JobConfEntity> jobs = createJobs(workflowCommand.getJobs(), workflow);
            workflow.setJobs(jobs);
            workflows.add(workflow);

            intents.add(AgentIntentEntity.builder().agentId(agent.getId()).description(workflowCommand.getDescription())
                    .workflowId(workflow.getId()).build());

            if (StringUtils.isNotBlank(workflowCommand.getSlash())) {
                slashes.add(AgentSlashEntity.builder().agentId(agent.getId()).workflowId(workflow.getId())
                        .name(workflowCommand.getSlash()).build());
            }
        }

        // 3、保存Slash和Intent数据
        agentRepository.saveOrUpdateAgentIntents(intents);
        agentRepository.saveOrUpdateAgentSlashs(slashes);

        return AgentAllConf.builder()
                .agent(agent)
                .slashes(slashes)
                .intents(intents)
                .workflows(workflows)
                .build();
    }

    @Override
    public AgentAllConf update(UpdateAgentCommand command) {
        // TODO: 8.11.23  
        return null;
    }

    @Override
    public AgentAllConf get(Long id) {
        // TODO: 8.11.23  
        return null;
    }

    @Override
    public void delete(Long id) {
        // TODO: 8.11.23  
    }

    /**
     * 创建Workflow
     * @param command
     * @param agent
     * @return
     */
    private WorkflowConfEntity createWorkflow(CreateWorkflowCommand command, AgentEntity agent) {
        WorkflowConfEntity workflow = WorkflowConfEntity.builder()
                .agentId(agent.getId())
                .executeType(command.getExecuteType())
                .name(command.getName())
                .build();
        workflowConfRepository.saveOrUpdateWorkflowConf(workflow);
        return workflow;
    }

    /**
     * 创建Job
     * @param jobs
     * @param workflow
     * @return
     */
    private List<JobConfEntity> createJobs(List<CreateJobCommand> jobs, WorkflowConfEntity workflow) {
        List<JobConfEntity> jobConfEntities = new ArrayList<>();
        Long upstreamId = -1L;
        for (CreateJobCommand job : jobs) {
            JobConfEntity jobConfEntity = JobConfEntity.builder()
                    .jobType(job.getJobType())
                    .canModify(job.isCanModify())
                    .inputs(GsonConstants.GSON.fromJson(job.getInputs(), INPUT_TYPE))
                    .outputs(GsonConstants.GSON.fromJson(job.getOutputs(), OUTPUT_TYPE))
                    .name(job.getName())
                    .prompt(new PromptTemplate(job.getPrompt()))
                    .specifyConfig(job.getSpecifyConfig())
                    .workflowId(workflow.getId())
                    .build();
            if (StringUtils.isNotBlank(job.getTools())) {
                jobConfEntity.setTools(ToolBox.fromTools(
                        Arrays.stream(job.getTools().split(","))
                                .map(toolType -> EnumUtils.getEnum(ToolType.class, toolType))
                                .toList()));
            }
            jobConfEntity.setUpstreamId(upstreamId);
            jobConfRepository.saveOrUpdate(jobConfEntity);

            jobConfEntities.add(jobConfEntity);
        }
        return jobConfEntities;
    }
}
