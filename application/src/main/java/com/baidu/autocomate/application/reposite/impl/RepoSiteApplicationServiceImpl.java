package com.baidu.autocomate.application.reposite.impl;

import com.baidu.autocomate.application.reposite.RepoSiteApplicationService;
import com.baidu.autocomate.application.reposite.convertor.RepoSiteConvertor;
import com.baidu.autocomate.application.reposite.dto.RepoSiteDTO;
import com.baidu.autocomate.domain.reposite.entity.RepoSiteEntity;
import com.baidu.autocomate.domain.reposite.service.RepoSiteDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RepoSiteApplicationServiceImpl implements RepoSiteApplicationService {

    @Autowired
    private RepoSiteDomainService repoSiteDomainService;

    @Autowired
    private RepoSiteConvertor repoSiteConvertor;

    @Override
    public List<RepoSiteDTO> getByKeyWord(String name, String repo) {
        List<RepoSiteEntity> repoSiteEntities = repoSiteDomainService.getByKeyWord(name, repo);
        return repoSiteConvertor.toDTOS(repoSiteEntities);
    }

    @Override
    public RepoSiteDTO getByName(String name) {
        RepoSiteEntity repoSiteEntity = repoSiteDomainService.getByName(name);
        return repoSiteConvertor.toDTO(repoSiteEntity);
    }

    @Override
    public RepoSiteDTO getByRepo(String repo) {
        RepoSiteEntity repoSiteEntity = repoSiteDomainService.getByRepo(repo);
        return repoSiteConvertor.toDTO(repoSiteEntity);
    }
}
