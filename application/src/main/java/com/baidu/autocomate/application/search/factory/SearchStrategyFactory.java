package com.baidu.autocomate.application.search.factory;

import com.baidu.autocomate.application.search.strategy.SearchStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SearchStrategyFactory {
    private static final Map<String, SearchStrategy> SEARCH_STRATEGY_MAP = new ConcurrentHashMap<>();

    /**
     * 注册策略
     */
    public static void registerStrategy(String name, SearchStrategy strategy) {
        SEARCH_STRATEGY_MAP.put(name, strategy);
    }
    /**
     * 获取策略
     */
    public static SearchStrategy getSearchStrategy(String name) {
        SearchStrategy searchStrategy = SEARCH_STRATEGY_MAP.get(name);
        if (searchStrategy == null) {
            throw new IllegalArgumentException("not support slash:" + name);
        }
        return searchStrategy;
    }
}
