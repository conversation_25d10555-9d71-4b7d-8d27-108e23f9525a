package com.baidu.autocomate.application.analyze.strategy.composer;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.common.prompts.PromptTemplate;
import com.baidu.autocomate.domain.llm.bean.CompletionContent;
import com.baidu.autocomate.domain.llm.bean.CompletionResult;
import com.baidu.autocomate.domain.llm.enums.LLMScene;
import com.baidu.autocomate.domain.llm.executor.LLMExecutor;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieAccountType;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieModelType;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieRequest;
import com.baidu.autocomate.domain.llm.utils.ParseUtils;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.shortcut.ShortcutPrompts;
import com.baidu.autocomate.domain.think.bean.SecurityAnalysisAttackType;
import com.baidu.autocomate.domain.think.bean.SecurityAnalysisResult;
import com.baidu.autocomate.domain.think.bean.SecurityAnalysisRule;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class ComposerSecurityAnalyzer implements LLMExecutor {

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static final ObjectMapper MAPPER = defaultObjectMapper();


    /**
     * 创建默认的ObjectMapper实例
     *
     * @return 配置好的ObjectMapper对象
     */
    private static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);
        mapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        return mapper;
    }


    /**
     * 分析代码安全性。
     * 先进行规则匹配，如果匹配到攻击行为，则直接返回。
     * 如果规则未匹配到攻击行为，则继续使用LLM进行分析。
     *
     * @param analyzeCommand 分析命令对象
     * @param analyzeResult  分析结果对象
     */
    public void analyzeCodeSecurity(AnalyzeCommand analyzeCommand, AnalyzeResult analyzeResult) {
        String query = analyzeCommand.getQuery();
        if (StringUtils.isEmpty(query)) {
            return;
        }

        // 先进行规则匹配
        SecurityAnalysisResult ruleBasedResult = analyzeByRules(query);
        if (ruleBasedResult.isAttack()) {
            analyzeResult.putExtend("securityAnalysisResult", ruleBasedResult);
            return;
        }

        // 如果规则匹配未发现问题，继续使用 LLM 分析
        // TODO input 拼装最终的userPrompt
        String input = query;
        try {
            String prompt = buildPrompt(input);
            // 请求模型
            ErnieModelType modelType = modelType();
            CompletionContent content = CompletionContent.builder()
                    .input(prompt)
                    .contentSource(MessageContentSource.MONGO)
                    .scene(LLMScene.COMPOSER_SECURITY_ANALYZER)
                    .role(ChatMessageRole.USER)
                    .username(analyzeCommand.getUserName())
                    .sessionId(null)
                    .model(modelType.name())
                    .build();
            ErnieRequest request = ErnieRequest.builder()
                    .ernieAccountType(ErnieAccountType.COMPOSER)
                    .ernieModelType(modelType)
                    .useSingle(false)
                    .temperature(0.45)
                    .topP(0.95)
                    .build();
            CompletionResult completionResult = chatCompletionWithErnie(content, request);
            String resultContent = completionResult.getContent();
            // 解析结果
            String jsonContent = ParseUtils.parseJsonStr(resultContent);
            SecurityAnalysisResult analysisResult = MAPPER.readValue(jsonContent, SecurityAnalysisResult.class);

            // 设置到analyzeResult中
            analyzeResult.putExtend("securityAnalysisResult", analysisResult);

        } catch (Exception e) {
            log.error("Composer security analysis error.", e);
            analyzeResult.putExtend("securityAnalysisResult", SecurityAnalysisResult.defaultAttackResult());
        }
    }

    /**
     * 基于预定义规则分析输入内容的安全性
     *
     * @param input 待分析的输入内容
     * @return SecurityAnalysisResult 安全分析结果
     */
    private SecurityAnalysisResult analyzeByRules(String input) {
        boolean isAttack = false;
        Set<SecurityAnalysisAttackType> attackTypes = new HashSet<>();
        for (SecurityAnalysisRule rule : SecurityAnalysisRule.COMPOSER_SECURITY_RULES) {
            if (rule.matches(input)) {
                isAttack = true;
                attackTypes.add(rule.getAttackType());
            }
        }
        return SecurityAnalysisResult.builder()
                .isAttack(isAttack)
                .attackTypes(attackTypes)
                .build();
    }


    /**
     * 获取当前使用的Ernie模型类型
     *
     * @return ErnieModelType 模型类型，默认返回ERNIE_4_8K_0613
     */
    private ErnieModelType modelType() {
        try {
            String model = redisTemplate.opsForValue().get("composer_security_analyze_model");
            if (model == null) {
                return ErnieModelType.ERNIE_4_8K_0613;
            }
            return ErnieModelType.valueOf(model);
        } catch (Exception e) {
            log.error("Failed to get model type for composer security analysis.", e);
            return ErnieModelType.ERNIE_4_8K_0613;
        }
    }

    /**
     * 构建安全分析的提示词
     *
     * @param input 用户输入内容
     * @return String 格式化后的提示词
     */
    private String buildPrompt(String input) {
        String promptTemplate = ShortcutPrompts.getComposerSecurityAnalyzePrompt();
        Map<String, String> params = Map.of("input", input);
        return PromptTemplate.formatPrompt(promptTemplate, params);
    }
}