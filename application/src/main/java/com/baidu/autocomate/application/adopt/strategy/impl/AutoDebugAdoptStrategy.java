package com.baidu.autocomate.application.adopt.strategy.impl;

import com.baidu.autocomate.application.adopt.cqe.AdoptCommand;
import com.baidu.autocomate.application.adopt.strategy.AdoptStrategy;
import com.baidu.autocomate.domain.adoption.service.AutoDebugAdoptionFacade;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AutoDebugAdoptStrategy implements AdoptStrategy {

    @Autowired
    private AutoDebugAdoptionFacade autoDebugAdoptionFacade;


    @Override
    public String getSlash() {
        return Slash.AUTO_DEBUG.getName();
    }

    @Override
    public void adopt(AdoptCommand adoptCommand) {
        autoDebugAdoptionFacade.uploadAdoption(adoptCommand.getAdoptionUuid());
    }
}
