package com.baidu.autocomate.application.metric;

import com.baidu.autocomate.application.metric.cqe.IdePasteMetricCommand;
import com.baidu.autocomate.application.metric.cqe.MessageLikeQuery;
import com.baidu.autocomate.application.metric.dto.MessageLikeExcelDTO;
import com.baidu.autocomate.application.metric.dto.RegisterConvertRateDTO;
import com.baidu.autocomate.application.metric.dto.RetenionMetricDTO;
import com.baidu.autocomate.domain.message.enums.LikeStatus;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.List;

public interface MetricApplicationService {
    RetenionMetricDTO getUserRetenion(LocalDateTime startDate, LocalDateTime endDate);

    RegisterConvertRateDTO getRegisterConvertRate(LocalDateTime startDate, LocalDateTime endDate);

    List<MessageLikeExcelDTO> getMessageLikeExcelDTOs(MessageLikeQuery query, LikeStatus likeStatus);

    void recordIdePasteMetric(@RequestBody IdePasteMetricCommand idePasteMetricCommand);
}
