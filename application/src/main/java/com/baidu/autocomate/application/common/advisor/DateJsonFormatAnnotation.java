package com.baidu.autocomate.application.common.advisor;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 避免每次都写{@link JsonFormat}各种属性，
 * <p>
 * shape = JsonFormat.Shape.STRING, <br/>pattern ="yyyy-MM-dd'T'HH:mm:ss.SSSXXX", <br/>timezone = "UTC"
 * </p>
 *
 * <AUTHOR>
 */
@Target({ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
        timezone = "UTC")
@JacksonAnnotationsInside
public @interface DateJsonFormatAnnotation {
}
