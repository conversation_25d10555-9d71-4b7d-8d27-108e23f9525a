/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.analyze.rewrite.impl;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baidu.autocomate.application.analyze.bean.IntelliTestRewriteResult;
import com.baidu.autocomate.application.analyze.rewrite.IntelliTestQueryRewrite;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import com.baidu.autocomate.domain.utils.TraceUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
@Service
public class IntelliTestQueryRewriteImpl implements IntelliTestQueryRewrite {

    @Value("${testmate.query.rewrite.url}")
    private String queryRewriteUrl;

    @Override
    public List<String> rewriteQuery(String query) {
        if (StringUtils.isBlank(query)) {
            return null;
        }
        Map<String, String> body = Map.of("query", query);
        Request.Builder builder = new Request.Builder()
                .url(queryRewriteUrl)
                .addHeader(TraceUtils.TRACE_ID, MDC.get(TraceUtils.TRACE_ID))
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(body)));

        Request request = builder.build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("testmate query rewrite, response:{}", response);
                return null;
            }
            String responseJson = response.body().string();
            log.info("testmate query rewrite: {}", responseJson);
            IntelliTestRewriteResult result = GSON.fromJson(responseJson, IntelliTestRewriteResult.class);
            if (HttpStatus.SC_OK == result.getCode()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("testmate query rewrite failed by params: {}", GsonConstants.GSON.toJson(request), e);
        }
        return null;
    }
}
