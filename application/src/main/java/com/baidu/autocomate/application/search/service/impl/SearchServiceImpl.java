package com.baidu.autocomate.application.search.service.impl;

import com.baidu.autocomate.application.search.bean.SearchResult;
import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.application.search.factory.SearchStrategyFactory;
import com.baidu.autocomate.domain.search.entity.SearchResultPersistentEntity;
import com.baidu.autocomate.domain.search.repository.SearchResultRepository;
import com.baidu.autocomate.domain.knowledge.code.bean.CodeChunk;
import com.baidu.autocomate.application.search.service.SearchService;
import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.searcher.SearchType;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import com.baidu.autocomate.domain.searcher.searcher.graph.GraphSearchResult;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

@Slf4j
@Service
public class SearchServiceImpl implements SearchService {
    @Autowired
    private SearchResultRepository searchResultRepository;

    @Override
    public SearchResult search(@NonNull SearchCommand searchCommand) {
        searchCommand.validate();
        SearchStrategy searchStrategy = SearchStrategyFactory.getSearchStrategy(searchCommand.getSlash());

        // 1. 检索
        log.info("search command: {}", GSON.toJson(searchCommand));
        DataSearchResult dataSearchResult = searchStrategy.search(searchCommand);
        log.info("search result: {}", GSON.toJson(dataSearchResult));
        // 2. 保存检索结果
        SearchResultPersistentEntity persistentEntity = SearchResultPersistentEntity.builder()
                .id(searchCommand.getMessageId())
                .searchResult(dataSearchResult).build();
        searchResultRepository.save(persistentEntity);
        // 返回代码向量检索的chunk，插件端据此获取代码片段
        com.baidu.autocomate.domain.searcher.result.SearchResult<?> codeChunksData =
                dataSearchResult.getData(SearchType.CODE);
        List<CodeChunk> codeChunks = codeChunksData == null ?
                Collections.emptyList() : (List<CodeChunk>) codeChunksData.getData();
        // 返回代码知识图谱缩略图，插件端据此展示缩略图
        com.baidu.autocomate.domain.searcher.result.SearchResult<?> graphSearchResult = dataSearchResult.getData(SearchType.CODE_GRAPH);
        Object graphRenderThumbnail = null;
        if (null != graphSearchResult && CollectionUtils.isNotEmpty(graphSearchResult.getData())) {
            graphRenderThumbnail = graphSearchResult.getData().stream()
                    .filter(Objects::nonNull)
                    .filter(graph -> graph instanceof GraphSearchResult && ((GraphSearchResult) graph).getGraphRenderThumbnail() != null)
                    .map(graph -> ((GraphSearchResult) graph).getGraphRenderThumbnail())
                    .findFirst();
        }

        return SearchResult.builder()
                .conversationId(searchCommand.getConversationId())
                .messageId(searchCommand.getMessageId())
                .codeChunks(codeChunks)
                .graphRenderThumbnail(graphRenderThumbnail)
                .queryType(codeChunksData == null ? null : codeChunksData.getQueryType())
                .build();
    }
}
