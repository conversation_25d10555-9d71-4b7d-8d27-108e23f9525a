package com.baidu.autocomate.application.common.cqe;
import com.baidu.autocomate.application.common.advisor.Validation;
import com.baidu.autocomate.domain.agent.enums.AgentInitType;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 消息命令类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserMessageCommand implements Validation {
    /**
     * 消息内容
     */
    private MessageContentEntity content;

    /**
     * 所属agentId
     */
    private Long agentId;
    
    private Long conversationId;
    
    /**
     * workflow执行的记录id
     */
    private Long workflowBuildId;

    private Long jobBuildId;

    private String knowledge;
    
    private String workspace;

    private AgentInitType initType;
    
    /**
     * 消息时间戳，以毫秒为单位
     */
    private Long timestamp;

    /**
     * 命令类型
     */
    private String slash;

    /**
     * taskId，用于标识当前命令属于哪个消息任务
     */
    private Long messageTaskId;

    /**
     * 验证消息命令是否符合规范：
     */
    @Override
    public void validate() {
        Assert.notNull(content, "content is null");
        Assert.notNull(agentId, "agentId is null");
        Assert.notNull(initType, "init Type is null");
    }
    
    public void cancelValidate() {
        validate();
        Assert.notNull(workflowBuildId, "workflow is null");
    }
}
