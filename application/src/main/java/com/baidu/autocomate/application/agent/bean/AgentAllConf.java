package com.baidu.autocomate.application.agent.bean;

import com.baidu.autocomate.domain.agent.entity.AgentEntity;
import com.baidu.autocomate.domain.agent.entity.AgentIntentEntity;
import com.baidu.autocomate.domain.agent.entity.AgentSlashEntity;
import com.baidu.autocomate.domain.workflow.conf.WorkflowConfEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentAllConf {

    private AgentEntity agent;

    private List<AgentIntentEntity> intents;

    private List<AgentSlashEntity> slashes;

    private List<WorkflowConfEntity> workflows;
}
