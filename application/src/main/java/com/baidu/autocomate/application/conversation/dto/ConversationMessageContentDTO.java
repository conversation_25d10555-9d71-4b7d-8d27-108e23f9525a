package com.baidu.autocomate.application.conversation.dto;

import com.baidu.autocomate.domain.jobs.executor.code.bean.CodeQAMode;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import com.baidu.autocomate.domain.message.enums.MessageContentType;
import com.baidu.autocomate.domain.message.enums.MessageInteractionMode;
import lombok.Data;

@Data
public class ConversationMessageContentDTO {

    private Long id;

    private Long messageId;

    /**
     * 消息分为不同的类型，用于前端、后端来区分不同的消息，前端可以做不同的渲染
     */
    private MessageContentType type;

    /**
     * 消息体
     */
    private String text;

    /**
     * Content的特殊信息，根据type的不同做渲染
     */
    private Object detail;

    /**
     * 表示是否追加文本内容，用于前端识别
     */
    private boolean append = false;

    /**
     * 消息发送者的sessionId
     * <p>由sender提供，用于区分不同轮次的对话，避免串消息</p>
     * <p>代码智能搜索的单次完整问答是一次session，避免串消息</p>
     */
    private String senderSessionId;

    /**
     * 如果走的是sse，会有emitterId用来标识对应的emitter，
     * <p>
     *     为什么不用{@link MessageContentEntity#senderSessionId} ? <br />
     *     因为{@link MessageContentEntity#senderSessionId} 的作用是为了串联用户的消息以及AutoComate回复的消息，如果用户选择了停止生成，然后重新生成，就有可能并行存在两个同样的senderSessionId，因为不能作为唯一标识。
     *     emitterId期望保证是一次请求，而不是一条消息
     * </p>
     */
    private String emitterId;

    private boolean isEnd = false;

    /**
     * 消息的交互模式，比如websocket, sse
     */
    private MessageInteractionMode interactionMode = MessageInteractionMode.WEBSOCKET;
    
    private CodeQAMode mode;
}
