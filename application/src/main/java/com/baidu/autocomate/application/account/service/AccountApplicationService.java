package com.baidu.autocomate.application.account.service;

import com.baidu.autocomate.application.account.cqe.BindLoginAccountCommand;
import com.baidu.autocomate.application.account.cqe.CreateUnLoginAccountCommand;
import com.baidu.autocomate.domain.account.entity.WebAccountEntity;
import com.baidu.autocomate.domain.account.enums.UserAccountType;

public interface AccountApplicationService {

    /**
     *
     * @param command
     * @return
     */
    WebAccountEntity createUnLoginUserAccount(CreateUnLoginAccountCommand command);

    /**
     *
     * @param uniqueIdentifier
     * @return
     */
    WebAccountEntity findUnLoginUserAccount(String uniqueIdentifier);

    UserAccountType getUserAccountType(String uniqueIdentifier);

    void bindUnLoginAccountToLicenceAccount(BindLoginAccountCommand command);
}
