package com.baidu.autocomate.application.analyze.factory;

import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import lombok.NonNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AnalyzeStrategyFactory {

    private static final Map<String, AnalyzeStrategy> ANALYZE_STRATEGY_MAP = new ConcurrentHashMap<>();

    /**
     * 注册分析策略
     *
     * @param slash
     * @return
     */
    public static void registerAnalyzeStrategy(@NonNull String slash, AnalyzeStrategy analyzeStrategy) {
        ANALYZE_STRATEGY_MAP.put(slash, analyzeStrategy);
    }

    /**
     * 根据传入的字符串参数获取分析策略
     *
     * @param slash 字符串参数，表示要获取的分析策略
     * @return 返回对应分析策略，如果参数为空或不存在对应的分析策略，则返回null
     * @throws IllegalArgumentException 如果传入的参数不支持，则抛出此异常
     */
    public static AnalyzeStrategy getAnalyzeStrategy(String slash) {
        AnalyzeStrategy analyzeStrategy = ANALYZE_STRATEGY_MAP.get(slash);
        if (analyzeStrategy == null) {
            throw new IllegalArgumentException("not support slash:" + slash);
        }
        return analyzeStrategy;
    }
}
