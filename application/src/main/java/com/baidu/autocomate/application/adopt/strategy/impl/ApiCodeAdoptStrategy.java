/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.strategy.impl;

import java.util.Objects;

import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.adopt.cqe.AdoptCommand;
import com.baidu.autocomate.application.adopt.strategy.AdoptStrategy;
import com.baidu.autocomate.domain.adoption.bean.TestMateCodeAdoptResult;
import com.baidu.autocomate.domain.adoption.cqe.IApiUploadCommand;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import com.baidu.autocomate.domain.workflow.enums.Slash;

import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
@Slf4j
public class ApiCodeAdoptStrategy implements AdoptStrategy {
    @Value("${iapi.adopt.url}")
    private String adoptUrl;

    @Override
    public String getSlash() {
        return Slash.API_CODE.getName();
    }

    @Override
    public void adopt(AdoptCommand adoptCommand) {

        HttpUrl httpUrl = Objects.requireNonNull(HttpUrl.parse(adoptUrl)).newBuilder()
                .addQueryParameter("username", adoptCommand.getUserName())
                .addQueryParameter("platform","comate")
                .addQueryParameter("uuid", adoptCommand.getAdoptionUuid())
                .build();

        IApiUploadCommand iApiUploadCommand = IApiUploadCommand.builder()
                .acceptContent(adoptCommand.getContentStr())
                .build();

        // 调用iapi采纳接口
        Request httpRequest = new Request.Builder()
                .url(httpUrl)
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(iApiUploadCommand)))
                .build();

        try (Response response = HttpConstants.codeSearchClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("adopt iapi code error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.info("adopt iapi code response: {}", responseJson);
            TestMateCodeAdoptResult
                    codeAdoptResult = GsonConstants.GSON.fromJson(responseJson, TestMateCodeAdoptResult.class);
            if (HttpStatus.SC_OK != codeAdoptResult.getCode()) {
                log.error("adopt iapi code error, response:{}", responseJson);
            }

        } catch (Exception e) {
            log.error("adopt iapi code error", e);
        }

    }
}
