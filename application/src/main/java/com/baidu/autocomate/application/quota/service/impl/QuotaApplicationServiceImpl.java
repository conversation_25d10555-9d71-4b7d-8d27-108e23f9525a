package com.baidu.autocomate.application.quota.service.impl;

import com.baidu.autocomate.application.account.service.AccountApplicationService;
import com.baidu.autocomate.application.permission.PermissionService;
import com.baidu.autocomate.application.quota.cqe.QuotaConfigRequest;
import com.baidu.autocomate.application.quota.cqe.UserQuotaQuery;
import com.baidu.autocomate.application.quota.service.QuotaApplicationService;
import com.baidu.autocomate.domain.account.enums.UserAccountType;
import com.baidu.autocomate.domain.quota.entity.QuotaConfigEntity;
import com.baidu.autocomate.domain.quota.entity.QuotaRecordEntity;
import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import com.baidu.autocomate.domain.quota.repos.QuotaRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class QuotaApplicationServiceImpl implements QuotaApplicationService {

    @Autowired
    private QuotaRepository quotaRepository;

    @Autowired
    private AccountApplicationService accountApplicationService;

    @Autowired
    private PermissionService permissionService;

    @Override
    public Integer queryUserQuotaLeft(UserQuotaQuery query) {
        // TODO 等 COMATE WEB 下线就删掉
        QuotaConfigEntity config = queryQuotaConfig(query);
        return queryUserQuotaLeft(query, config);
    }

    @Override
    public Integer queryUserQuotaLeft(UserQuotaQuery query, QuotaConfigEntity config) {
        if (config == null) {
            return NumberUtils.INTEGER_ZERO;
        }

        Integer limit = config.getLoginTimes();
        return quotaRepository.queryUserQuotaLeft(query.getSubjectId(), query.getResourceType(), limit, query.getTimeRange());
    }

    @Override
    public QuotaConfigEntity queryQuotaConfig(UserQuotaQuery query) {
        return quotaRepository.queryQuotaConfig(
                query.getSubjectType(),
                query.getSubjectType() == SubjectType.DEFAULT ? null : query.getSubjectId(),
                query.getResourceType(),
                query.getTimeRange()
        );
    }

    @Override
    public Integer queryUserQuotaLimit(UserQuotaQuery query) {
        // COMATE_WEB 特殊处理
        if (ResourceType.COMATE_WEB.equals(query.getResourceType())) {
            // 系统管理员特殊处理
            if (permissionService.isSystemManager(query.getSubjectId())) {
                return Integer.MAX_VALUE;
            }

            // Comate Web 的配额配置是默认的，没有用户特殊配置
            QuotaConfigEntity config = quotaRepository.queryQuotaConfig(SubjectType.DEFAULT, null,
                    query.getResourceType(), TimeRange.TOTAL);
            if (config != null) {
                UserAccountType userType = accountApplicationService.getUserAccountType(query.getSubjectId());
                return config.getQuotaLimit(userType);
            }
            return NumberUtils.INTEGER_ZERO;
        }

        // 其他资源类型正常处理
        QuotaConfigEntity config = queryQuotaConfig(query);
        return config != null ? config.getLoginTimes() : NumberUtils.INTEGER_ZERO;
    }

    @Override
    public boolean allowAndRecordUserQuota(String userIdentifier, ResourceType resourceType, Integer limit) {
        return quotaRepository.allowAndRecordUserQuota(userIdentifier, resourceType, limit, TimeRange.TOTAL);
    }

    @Override
    public void recordUserQuota(String userIdentifier, ResourceType resourceType) {
        quotaRepository.recordUserQuota(QuotaRecordEntity.builder()
                .userIdentifier(userIdentifier)
                .resourceType(resourceType)
                .createAt(LocalDateTime.now())
                .build());
    }

    @Override
    public void saveOrUpdateQuotaConfig(QuotaConfigRequest request) {
        try {
            QuotaConfigEntity config = QuotaConfigEntity.builder()
                    .subjectType(request.getSubjectType())
                    .subjectId(request.getSubjectId())
                    .resourceType(request.getResourceType())
                    .timeRange(request.getTimeRange())
                    .loginTimes(request.getLoginTimes())
                    .build();
            quotaRepository.saveOrUpdateQuotaConfig(config);
        } catch (Exception e) {
            log.error("save or update quota config failed, check param: {}", request, e);
            throw new IllegalArgumentException("save or update quota config failed");
        }
    }
}
