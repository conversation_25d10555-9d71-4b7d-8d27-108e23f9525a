package com.baidu.autocomate.application.analyze.cqe;

import com.baidu.autocomate.application.common.advisor.Validation;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.i18n.MessageHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnalyzeCommand implements Serializable, Validation {
    private static final long serialVersionUID = -7326497708227045176L;

    /**
     * 会话ID
     */
    private Long conversationId;
    /**
     * 消息ID
     */
    private Long messageId;
    /**
     * 代码库ID
     */
    private String repoId;
    /**
     * 是否进行意图识别
     */
    private boolean intent;
    /**
     * 是否进行图谱检索
     */
    private boolean needGraph;
    /**
     * 指令，根据slash判断要执行哪些逻辑
     */
    private String slash;
    /**
     * 用户输入的query
     */
    private String query;
    /**
     * 是否需要Query改写
     */
    private boolean needQueryRewrite;

    /**
     * 选中的代码块
     */
    private String selection;

    private String userName;

    /**
     * 选择的上下文
     */
    private List<AutoworkContext> contexts;

    /**
     * ide类型
     */
    private String ide;

    /**
     * 插件版本
     */
    private String pluginVersion;

    /**
     * 当前文件路径
     */
    private String currentFilePath;

    /**
     * 光标所在行
     */
    private Integer cursorLine;

    /**
     * 模型唯一标识，表示用户选择要使用的模型
     * 在AnalyzeCommand中加该字段的原因：
     * 如果用户选择默认模型，Composer 要做白名单检查、配额检查、安全分析，
     * 如果用户选择自定义模型，Composer 放行。
     */
    private String modelKey;

    @Override
    public void validate() {
        Assert.notNull(userName, MessageHolder.get("param.is.null", "userName"));
        Assert.notNull(slash, MessageHolder.get("param.is.null", "slash"));
    }
}
