package com.baidu.autocomate.application.account.cqe;

import com.baidu.autocomate.application.common.advisor.Validation;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

@Data
@ToString
public class BindLoginAccountCommand implements Validation {

    /**
     * 未登录账号，是我们自己服务生成的
     */
    private String unLoginAccount;

    /**
     * comate的license， 登录后，调用comate /api/user 接口获取的
     */
    private String loginAccount;

    /**
     * 登录用户的来源，这个是前端从comate服务的 /api/user接口取的，然后传到这个接口里去的
     */
    private String source;

    @Override
    public void validate() {
        Assert.isTrue(StringUtils.isNotBlank(unLoginAccount), "unlogin user identifier is blank");
        Assert.isTrue(StringUtils.isNotBlank(loginAccount), "login user licence is blank");
        Assert.isTrue(!unLoginAccount.equals(loginAccount),  "unlogin and login account must not be equal");
    }
}