package com.baidu.autocomate.application.yiyan.service.impl;

import com.baidu.autocomate.application.yiyan.cqe.YiYanWebSearchQuery;
import com.baidu.autocomate.application.yiyan.dto.YiYanActionContentDTO;
import com.baidu.autocomate.application.yiyan.dto.YiYanWebSearchResponse;
import com.baidu.autocomate.application.yiyan.service.YiYanApplicationService;
import com.baidu.autocomate.application.yiyan.utils.YiYanUtils;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.jobs.executor.code.bean.IndexNumContext;
import com.baidu.autocomate.domain.searcher.DataSearchHelper;
import com.baidu.autocomate.domain.searcher.SearchType;
import com.baidu.autocomate.domain.searcher.param.SearchParam;
import com.baidu.autocomate.domain.searcher.param.SearchParams;
import com.baidu.autocomate.domain.searcher.param.WebSearchParam;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import com.baidu.autocomate.domain.searcher.result.WebSearchResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class YiYanApplicationServiceImpl implements YiYanApplicationService {

    @Autowired
    private DataSearchHelper dataSearchHelper;

    /**
     *  返回一个可以直接放到一言prompt的字符串，占用大模型的token的，经和xiapeng02确定，为3000个字符
     */
    @Value("${yiyan.web.search.characters.limit:3000}")
    private int webSearchCharactersLimit;

    @Override
    public YiYanWebSearchResponse yiYanWebSearch(YiYanWebSearchQuery query) {
        Map<SearchType, List<SearchParam>> searchParamMap = new HashMap<>();
        WebSearchParam webSearchParam = WebSearchParam.builder().webQuery(query.getKeywords()).build();
        searchParamMap.put(SearchType.WEB, Lists.newArrayList(webSearchParam));
        SearchParams searchParams = SearchParams.builder()
                .searchTypes(Lists.newArrayList(SearchType.WEB))
                .query(query.getKeywords())
                .searchParamMap(searchParamMap)
                .username("yiyan")
                .build();
        DataSearchResult dataSearchResult = dataSearchHelper.search(searchParams);
        List<WebSearchResult> allSearchResults = dataSearchResult.getTypeData(SearchType.WEB);
        // 组装返回一言，提供给web页面展示使用。注意会将搜索出来的所有结果都返回给页面展示(至于是否真的展示前端调整)
        YiYanActionContentDTO yiYanActionContentDTO = YiYanUtils.genYiYanActionContent(allSearchResults);
        List<WebSearchResult> trimmedSearchResults = WebSearchResult.trimWebSearchResult(webSearchCharactersLimit,
                allSearchResults);
        // todo 看看webSearchResult 是不是要加个 "web搜索结果如下这样的字样"
        String webSearchResult = WebSearchResult.generateKnowledgeSnippets(trimmedSearchResults, new IndexNumContext());
        return YiYanWebSearchResponse.builder().searchResultForDisplay(GsonConstants.GSON.toJson(yiYanActionContentDTO))
                .techSearchResult(webSearchResult).build();
    }
}
