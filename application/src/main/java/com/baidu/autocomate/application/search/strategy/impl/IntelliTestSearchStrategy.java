/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.search.strategy.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.baidu.autocomate.domain.knowledge.code.bean.KnowledgeQueryBean;
import com.baidu.autocomate.domain.knowledge.code.bean.KnowledgeSearchRequest;
import com.baidu.autocomate.domain.knowledge.code.bean.Repo;
import com.baidu.autocomate.domain.searcher.DataSearchHelper;
import com.baidu.autocomate.domain.searcher.SearchType;
import com.baidu.autocomate.domain.searcher.param.CodeSearchParam;
import com.baidu.autocomate.domain.searcher.param.KnowledgeSearchParam;
import com.baidu.autocomate.domain.searcher.param.SearchParam;
import com.baidu.autocomate.domain.searcher.param.SearchParams;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 智能测试检索策略
 *
 * 与code_qa检索策略类似，只是在代码检索的时候，需要同时检索测试代码
 */
@Component
public class IntelliTestSearchStrategy implements SearchStrategy {
    @Autowired
    private DataSearchHelper dataSearchHelper;

    @Override
    public String getSlash() {
        return Slash.INTELLI_TEST.getName();
    }

    @Override
    public DataSearchResult search(SearchCommand searchCommand) {

        // 构建搜索参数
        List<String> paths = new ArrayList<>();
        List<AutoworkContext> contexts = searchCommand.getContexts();
        // 构建repo参数
        final List<Repo> repos = Lists.newArrayList(
                Repo.builder().repo(searchCommand.getRepoId()).repoId(searchCommand.getRepoId()).build());
        boolean needCodeSearch = false;
        // 构建知识搜索参数
        final List<KnowledgeQueryBean> knowledgeQueryBeans = Lists.newArrayList();
        // 构建URL抓取的搜索参数
        final List<String> urlKnowledgeUUIDs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contexts)) {
            // 要检索的目录和文件范围
            contexts.stream().filter(c -> AutoworkContextType.FILE.equals(c.getType())
                            || AutoworkContextType.FOLDER.equals(c.getType()))
                    .forEach(c -> paths.add(c.getId()));
            boolean hasRepoId = contexts.stream().anyMatch(c -> AutoworkContextType.REPO.equals(c.getType()));
            // 如果选择了代码库，或者文件、目录范围，则进行代码检索
            if (hasRepoId || CollectionUtils.isNotEmpty(paths)) {
                needCodeSearch = true;
            }

            // 构建查询系统和普通知识集以及iAPI项目对应的知识集的搜索参数
            contexts.stream().filter(c -> AutoworkContextType.SYSTEM.equals(c.getType())
                            || AutoworkContextType.NORMAL.equals(c.getType())
                            || AutoworkContextType.PERSONAL.equals(c.getType())
                            || AutoworkContextType.DEPARTMENT.equals(c.getType())
                            || AutoworkContextType.API_PROJECT.equals(c.getType()))
                    .forEach(c -> {
                        String type = c.getType().name();
                        if (AutoworkContextType.API_PROJECT.equals(c.getType())) {
                            type = "IAPI";
                        }
                        KnowledgeQueryBean knowledgeQueryBean = KnowledgeQueryBean.builder()
                                .type(type).uuid(c.getId()).build();
                        knowledgeQueryBeans.add(knowledgeQueryBean);
                    });

            // URL抓取的知识、或者单个API的知识
            contexts.stream().filter(c -> AutoworkContextType.URL.equals(c.getType())
                            || AutoworkContextType.API.equals(c.getType()))
                    .forEach(c -> urlKnowledgeUUIDs.add(c.getId()));
        }
        // 构建代码搜索参数,需要检索测试代码
        SearchParam codeEmbeddingSearchParam = CodeSearchParam.builder()
                .license(searchCommand.getUserName())
                .repos(repos)
                .subQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                .paths(paths)
                .tool("testmate")
                .codeSearchType(CodeSearchParam.CodeSearchType.VECTOR)
                .build();

        SearchParam knowledgeEmbeddingSearchParam = KnowledgeSearchParam.builder()
                .source("COMATE")
                .knowledgeRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT)
                .knowledgeQueryBeans(knowledgeQueryBeans)
                .knowledgeUUIDs(urlKnowledgeUUIDs)
                .build();

        Map<SearchType, List<SearchParam>> searchParamMap = Maps.newHashMapWithExpectedSize(2);
        if (needCodeSearch) {
            searchParamMap.put(SearchType.CODE, Lists.newArrayList(codeEmbeddingSearchParam));
        }
        if (CollectionUtils.isNotEmpty(knowledgeQueryBeans)
                || CollectionUtils.isNotEmpty(urlKnowledgeUUIDs)) {
            searchParamMap.put(SearchType.KNOWLEDGE, Lists.newArrayList(knowledgeEmbeddingSearchParam));
        }
        SearchParams searchParams = SearchParams.builder()
                .searchTypes(Lists.newArrayList(SearchType.CODE, SearchType.KNOWLEDGE))
                .username(searchCommand.getUserName())
                .device(searchCommand.getDevice())
                .query(searchCommand.getQuery())
                .searchParamMap(searchParamMap)
                .build();
        DataSearchResult dataSearchResult = dataSearchHelper.search(searchParams);
        return dataSearchResult;
    }
}
