package com.baidu.autocomate.application.conversation.service.imp;

import com.baidu.autocomate.application.conversation.cqe.AutoworkConversationCreateCommand;
import com.baidu.autocomate.application.conversation.cqe.MessageCreateCommand;
import com.baidu.autocomate.application.conversation.service.AutoworkConversationService;
import com.baidu.autocomate.domain.agent.utils.CodeAgentUtils;
import com.baidu.autocomate.domain.conversation.entity.ConversationEntity;
import com.baidu.autocomate.domain.conversation.enums.ConversationType;
import com.baidu.autocomate.domain.conversation.repos.ConversationRepository;
import com.baidu.autocomate.domain.message.entity.MessageEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class AutoworkConversationServiceImpl implements AutoworkConversationService {
    @Autowired
    private ConversationRepository conversationRepository;
    @Autowired
    private CodeAgentUtils codeAgentUtils;

    @Override
    public ConversationEntity createConversation(AutoworkConversationCreateCommand conversationCreateCommand) {
        ConversationEntity conversation = ConversationEntity.builder()
                .conversationType(ConversationType.SINGLE)
                .agentId(codeAgentUtils.getAgentId())
                .createAt(LocalDateTime.now())
                .createBy(conversationCreateCommand.getUser())
                .source("IDE").build();
        conversationRepository.saveOrUpdate(conversation);
        return conversation;
    }

    @Override
    public MessageEntity addMessage(ConversationEntity conversation, MessageCreateCommand messageCreateCommand) {

        return null;
    }
}
