/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.analyze.strategy.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.rewrite.IntelliTestQueryRewrite;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.jobs.executor.code.QueryUtil;
import com.baidu.autocomate.domain.workflow.enums.Slash;

/**
 * 智能测试的改写策略
 */
@Component
public class IntelliTestQueryRewriteStrategy implements AnalyzeStrategy {
    @Autowired
    private QueryUtil queryUtil;

    @Autowired
    private IntelliTestQueryRewrite intelliTestQueryRewrite;

    @Override
    public String getSlash() {
        return Slash.INTELLI_TEST.getName();
    }

    @Override
    public AnalyzeResult analyze(AnalyzeCommand analyzeCommand) {
        List<String> queries = null;
        if (analyzeCommand.isNeedQueryRewrite()) {
            queries = intelliTestQueryRewrite.rewriteQuery(analyzeCommand.getQuery());
            if (queries == null) {
                // 智能测试改写失败，使用默认的改写方法
                queries = queryUtil.rewriteQuery(null, analyzeCommand.getQuery(), analyzeCommand.getUserName());
            }
        }
        AnalyzeResult analyzeResult = new AnalyzeResult();
        analyzeResult.setCodeSearchQueries(queries);
        // TODO 分析queryType
        analyzeResult.setQueryType(null);
        return analyzeResult;
    }
}
