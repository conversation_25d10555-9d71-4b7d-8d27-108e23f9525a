package com.baidu.autocomate.application.search.bean;

import com.baidu.autocomate.domain.knowledge.code.bean.CodeChunk;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class SearchResult {
    private Long conversationId;
    private Long messageId;
    /**
     * 搜索到的远端代码块
     */
    private List<CodeChunk> codeChunks;

    /**
     * 代码图谱缩略图渲染信息
     */
    private Object graphRenderThumbnail;
    /**
     * 查询类型，用于判断是否是架构信息类查询
     */
    private String queryType;
}
