package com.baidu.autocomate.application.analyze.strategy;

import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.factory.AnalyzeStrategyFactory;

import javax.annotation.PostConstruct;

public interface AnalyzeStrategy {
    @PostConstruct
    default void init() {
        AnalyzeStrategyFactory.registerAnalyzeStrategy(getSlash(), this);
    }

    String getSlash();

    AnalyzeResult analyze(AnalyzeCommand analyzeCommand);
}
