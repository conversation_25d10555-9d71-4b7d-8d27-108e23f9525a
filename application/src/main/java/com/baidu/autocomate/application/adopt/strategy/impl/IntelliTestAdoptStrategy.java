/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.strategy.impl;

import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.adopt.cqe.AdoptCommand;
import com.baidu.autocomate.application.adopt.strategy.AdoptStrategy;
import com.baidu.autocomate.domain.adoption.bean.TestMateCodeAdoptResult;
import com.baidu.autocomate.domain.adoption.cqe.TestMateAdoptCommand;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import com.baidu.autocomate.domain.workflow.enums.Slash;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
@Slf4j
public class IntelliTestAdoptStrategy implements AdoptStrategy {

    @Value("${testmate.adopt.url}")
    private String adoptUrl;

    @Override
    public String getSlash() {
        return Slash.INTELLI_TEST.getName();
    }

    @Override
    public void adopt(AdoptCommand adoptCommand) {

        TestMateAdoptCommand testMateAdoptCommand = TestMateAdoptCommand.builder()
                .acceptContent(adoptCommand.getContentStr())
                .generateRecordUuid(adoptCommand.getAdoptionUuid())
                .build();

        // 调用testmate生成接口
        Request httpRequest = new Request.Builder()
                .url(adoptUrl)
                .addHeader("x-baidu-int-username", adoptCommand.getUserName())
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(testMateAdoptCommand)))
                .build();

        try (Response response = HttpConstants.codeSearchClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("adopt testmate error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.info("adopt testmate response:{}", responseJson);
            TestMateCodeAdoptResult
                    codeAdoptResult = GsonConstants.GSON.fromJson(responseJson, TestMateCodeAdoptResult.class);
            if (HttpStatus.SC_OK != codeAdoptResult.getCode()) {
                log.error("adopt testmate error, response:{}", responseJson);
            }

        } catch (Exception e) {
            log.error("adopt testmate  error", e);
        }

    }

}
