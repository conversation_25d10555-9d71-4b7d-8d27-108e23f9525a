package com.baidu.autocomate.application.reposite.convertor;

import com.baidu.autocomate.application.reposite.dto.RepoSiteDTO;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.reposite.entity.RepoSiteEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", imports = GsonConstants.class)
public interface RepoSiteConvertor {

    RepoSiteDTO toDTO(RepoSiteEntity repoSiteEntity);

    RepoSiteEntity toEntity(RepoSiteDTO repoSiteDTO);

    List<RepoSiteDTO> toDTOS(List<RepoSiteEntity> repoSiteEntityList);

    List<RepoSiteEntity> toEntities(List<RepoSiteDTO> repoSiteDTOList);
}
