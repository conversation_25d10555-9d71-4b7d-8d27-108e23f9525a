/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.search.strategy.impl;

import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.workflow.enums.Slash;

/**
 * 生成API调用代码，与智能问答-V2相同
 */
@Component
public class ApiCodeSearchStrategy extends IntelliTestSearchStrategy implements SearchStrategy {

    @Override
    public String getSlash() {
        return Slash.API_CODE.getName();
    }
}
