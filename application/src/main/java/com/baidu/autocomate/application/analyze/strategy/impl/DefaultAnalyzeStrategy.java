package com.baidu.autocomate.application.analyze.strategy.impl;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.baidu.autocomate.domain.jobs.executor.code.QueryUtil;
import com.baidu.autocomate.domain.llm.entity.LLMSessionEntity;
import com.baidu.autocomate.domain.llm.enums.LLMScene;
import com.baidu.autocomate.domain.llm.repos.LLMSessionRepository;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.searcher.searcher.code.RepoMetaCodeRetrieval;
import com.baidu.autocomate.domain.searcher.searcher.code.RepoMetaInfo;
import com.baidu.autocomate.domain.searcher.searcher.code.RepoMetaType;
import com.baidu.autocomate.domain.think.bean.IntentContext;
import com.baidu.autocomate.domain.think.bean.SearchRule;
import com.baidu.autocomate.domain.think.bean.SearchStrategy;
import com.baidu.autocomate.domain.think.bean.ThinkIntent;
import com.baidu.autocomate.domain.think.bean.ThinkOrigin;
import com.baidu.autocomate.domain.think.handler.IntentHandler;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.baidu.autocomate.domain.think.handler.IntentHandler.INTENT_API_VERSION_V2;

/**
 * 默认的Query改写策略
 */
@Component
@Slf4j
public class DefaultAnalyzeStrategy implements AnalyzeStrategy {

    @Autowired
    private QueryUtil queryUtil;
    @Autowired
    private IntentHandler intentHandler;
    @Autowired
    private LLMSessionRepository llmSessionRepository;
    @Autowired
    private RepoMetaCodeRetrieval repoMetaCodeRetrieval;

    @Override
    public String getSlash() {
        return Slash.CODE_QA.getName();
    }

    @Override
    public AnalyzeResult analyze(AnalyzeCommand analyzeCommand) {
        AnalyzeResult analyzeResult = new AnalyzeResult();
        if (analyzeCommand.isIntent()) {
            handleIntent(analyzeCommand, analyzeResult);
        } else {
            List<String> queries = null;
            if (analyzeCommand.isNeedQueryRewrite()) {
                queries = queryUtil.rewriteQuery(null, analyzeCommand.getQuery(), analyzeCommand.getUserName());
            }
            analyzeResult.setCodeSearchQueries(queries);
        }
        return analyzeResult;
    }

    /**
     * 意图识别
     *
     * @param analyzeCommand analyzeCommand对象
     * @param analyzeResult  analyzeResult对象，用于存储意图识别后的搜索策略
     */
    protected void handleIntent(AnalyzeCommand analyzeCommand, AnalyzeResult analyzeResult) {
        Long sessionId = getSessionId(analyzeCommand.getConversationId());
        // 组装意图上下文
        List<IntentContext> intentContexts = generateIntentContext(analyzeCommand);
        // 调用意图识别能力
        ThinkIntent thinkIntent = intentHandler.intent(analyzeCommand.getQuery(), ThinkOrigin.IDE, sessionId,
                LLMScene.CODE_QA, MessageContentSource.MONGO, intentContexts,
                analyzeCommand.getRepoId(), INTENT_API_VERSION_V2, analyzeCommand.getUserName(),
                analyzeCommand.getCurrentFilePath(), analyzeCommand.getCursorLine(), analyzeCommand.isNeedGraph());
        analyzeResult.setIsTech(thinkIntent.isTech());
        List<SearchStrategy> searchStrategies =
                convertSearchStrategy(analyzeCommand.getRepoId(), analyzeCommand.getQuery(),
                        thinkIntent.getSearchStrategies());
        // 知识图谱检索策略处理
        processGraphSearchStrategy(analyzeCommand, searchStrategies);
        analyzeResult.setIntentStrategies(searchStrategies);
        // 如果携带上下文，则进行query改写，提高检索效果
        if (needQueryRewrite(analyzeCommand, analyzeResult)) {
            List<String> queries = queryUtil.rewriteQuery(thinkIntent.getHistoryQueries(), analyzeCommand.getQuery(), analyzeCommand.getUserName());
            analyzeResult.setCodeSearchQueries(queries);
        }
    }

    private boolean needQueryRewrite(AnalyzeCommand analyzeCommand, AnalyzeResult analyzeResult) {
        List<AutoworkContext> contexts = analyzeCommand.getContexts();
        List<SearchStrategy> intentStrategies = analyzeResult.getIntentStrategies();
        if (CollectionUtils.isEmpty(contexts) && CollectionUtils.isEmpty(intentStrategies)) {
            return false;
        }

        return intentStrategies.stream()
                .anyMatch(strategy -> SearchRule.NEED_QUERY_REWRITE_RULES.contains(strategy.getRule()));
    }

    /**
     * 知识图谱检索策略处理
     * <li>需要根据analyzeCommand.isNeedGraph()来判断是否需要执行知识图谱检索策略</li></li>
     * <li>暂时只支持单文件的检索，如果超过两个文件则只保留第一个文件的知识图谱检索策略</li></li>
     * @param analyzeCommand analyzeCommand对象
     * @param searchStrategies 搜索策略列表
     * @return 处理后的搜索策略列表
     */
    private void processGraphSearchStrategy(AnalyzeCommand analyzeCommand, List<SearchStrategy> searchStrategies) {
        if (CollectionUtils.isEmpty(searchStrategies)) {
            return;
        }
        Optional<SearchStrategy> graphSearchStrategy = searchStrategies.stream().filter(strategy -> strategy.getRule() == SearchRule.FILE_GRAPH).findFirst();
        // 移除已有的知识图谱检索策略，如果需要执行知识图谱检索策略，则重新添加第一个
        searchStrategies.removeIf(strategy -> strategy.getRule() == SearchRule.FILE_GRAPH);
        if (graphSearchStrategy.isPresent() && analyzeCommand.isNeedGraph()) {
            searchStrategies.add(graphSearchStrategy.get());
        }
    }

    /**
     * 根据conversationId获取sessionId
     *
     * @param conversationId conversationId
     * @return 如果存在sessionId，则返回sessionId，否则返回null
     */
    private Long getSessionId(Long conversationId) {
        if (conversationId != null) {
            LLMSessionEntity llmSessionEntity =
                    llmSessionRepository.getSessionByConversationId(conversationId);
            return llmSessionEntity != null ? llmSessionEntity.getId() : null;
        }
        return null;
    }

    /**
     * 将元信息的读取改成读文件
     *
     * @param searchStrategies 意图识别得到的执行策略
     * @return 转换后的执行策略
     */
    private List<SearchStrategy> convertSearchStrategy(String repoId, String query,
                                                       List<SearchStrategy> searchStrategies) {
        if (CollectionUtils.isEmpty(searchStrategies)) {
            return Collections.emptyList();
        }
        // 判断strategies中是否包含元信息类型的策略，如果包含，则需要请求元信息读取接口，然后根据strategy的rule获取对应的meta信息
        boolean isNeedMetaInfo = searchStrategies.stream().filter(Objects::nonNull)
                .filter(strategy -> strategy.getRule() != null).anyMatch(searchStrategy ->
                        searchStrategy.getRule().isMetaRule());
        if (!isNeedMetaInfo) {
            return searchStrategies;
        }

        // 获取元信息
        RepoMetaInfo repoMetaInfo = repoMetaCodeRetrieval.retrievalMeta(repoId,
                Lists.newArrayList(RepoMetaType.BUILD, RepoMetaType.ENTRY));
        log.info("repoMetaInfo repoId: {} , repoMetaInfo: {}", repoId, repoMetaInfo);
        // 遍历strategies，如果包含元信息类型的策略，则根据rule获取对应的meta信息
        List<SearchStrategy> addedStrategies = new ArrayList<>();
        searchStrategies.stream().filter(Objects::nonNull)
                .filter(strategy -> strategy.getRule() != null && strategy.getRule().isMetaRule())
                .forEach(strategy -> {
                    // 通过rule和metaInfo field的映射获取对应的meta info
                    if (strategy.getRule() == SearchRule.BUILD_READ) {
                        // 处理构建信息
                        constructBuildInfo(addedStrategies, repoMetaInfo);
                    } else if (strategy.getRule() == SearchRule.STARTUP_READ) {
                        // 处理启动信息
                        constructStartInfo(addedStrategies, repoMetaInfo);
                    }
                });
        searchStrategies.addAll(addedStrategies);
        // 如果没有REPO_VECTOR，则添加REPO_VECTOR作为兜底
        if (searchStrategies.stream().noneMatch(strategy -> strategy.getRule() == SearchRule.REPO_VECTOR)) {
            SearchStrategy strategy = SearchStrategy.builder()
                    .rule(SearchRule.REPO_VECTOR)
                    .context(IntentContext.builder().type(IntentContext.IntentContextType.REPO)
                            .id(repoId).name("#当前代码库").build())
                    .query(query)
                    .build();
            searchStrategies.add(strategy);
        }
        return searchStrategies;
    }

    private void constructStartInfo(List<SearchStrategy> searchStrategies, RepoMetaInfo repoMetaInfo) {
        Map<String, RepoMetaInfo.RepoEntryInfo> entryInfoMap = repoMetaInfo.getEntry();
        if (MapUtils.isEmpty(entryInfoMap)) {
            return;
        }
        entryInfoMap.values().stream()
                .filter(Objects::nonNull)
                .forEach(repoEntryInfo -> {
                    // 读取path
                    if (repoEntryInfo.getEntryPaths() != null) {
                        repoEntryInfo.getEntryPaths().forEach(path -> {
                            SearchStrategy strategy = SearchStrategy.builder()
                                    .rule(SearchRule.FILE_READ)
                                    .context(IntentContext.builder().type(IntentContext.IntentContextType.FILE)
                                            .id(path).build())
                                    .build();
                            searchStrategies.add(strategy);
                        });
                    }

                    if (repoEntryInfo.getEntryFunctions() != null) {
                        repoEntryInfo.getEntryFunctions()
                                .forEach(func -> {
                                    SearchStrategy strategy = SearchStrategy.builder()
                                            .rule(SearchRule.FILE_READ)
                                            .context(IntentContext.builder().type(IntentContext.IntentContextType.FILE)
                                                    .id(func.getFilePath()).build())
                                            .build();
                                    searchStrategies.add(strategy);
                                });
                    }
                });

    }

    private static void constructBuildInfo(List<SearchStrategy> searchStrategies, RepoMetaInfo repoMetaInfo) {
        Map<String, List<String>> buildInfo = repoMetaInfo.getBuild();
        if (MapUtils.isEmpty(buildInfo)) {
            return;
        }
        // 只取前15个构建文件，每个构建文件最多添加3个策略
        buildInfo.values().stream()
                .filter(CollectionUtils::isNotEmpty)
                .limit(15)
                .forEach(paths -> {
                    searchStrategies.addAll(paths.stream().map(path -> SearchStrategy.builder()
                            .rule(SearchRule.FILE_READ)
                            .context(IntentContext.builder().type(IntentContext.IntentContextType.FILE)
                                    .id(path).build())
                            .build()).limit(3)
                            .toList());
                });
    }

    private List<IntentContext> generateIntentContext(AnalyzeCommand analyzeCommand) {
        if (CollectionUtils.isEmpty(analyzeCommand.getContexts())) {
            return Collections.emptyList();
        }

        return analyzeCommand.getContexts().stream()
                .map(context -> {
                    IntentContext.IntentContextType intentContextType =
                            EnumUtils.getEnum(IntentContext.IntentContextType.class, context.getType().name());
                    // 修正REPO和CURRENT_FILE的name，为意图识别容错
                    String name = context.getName();
                    if (context.getType() == AutoworkContextType.REPO) {
                        name = "#当前代码库";
                    } else if (context.getType() == AutoworkContextType.CURRENT_FILE) {
                        name = "#当前文件";
                    }
                    return IntentContext.builder().id(context.getId())
                            .type(intentContextType)
                            .retrievalType(context.getRetrievalType())
                            .name(name)
                            .build();
                })
                .toList();
    }
}
