package com.baidu.autocomate.application.conversation.dto;

import com.baidu.autocomate.application.common.advisor.DateJsonFormatAnnotation;
import com.baidu.autocomate.domain.agent.enums.AgentInitType;
import com.baidu.autocomate.domain.jobs.enums.JobState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZoneId;
import java.time.ZonedDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationDTO {
    
    private static final ZonedDateTime ZERO = ZonedDateTime.of(1970, 1, 1, 0, 0, 
            0, 0, ZoneId.systemDefault());
    
    private Long conversationId;

    /**
     * 当前正在运行的workflow build
     */
    private Long workflowBuildId;
    
    private String title;
    
    private JobState state;

    @DateJsonFormatAnnotation
    @Builder.Default
    private ZonedDateTime lastMessageTime = ZERO;
    
    private String knowledge;
    
    private String workspace;
    
    private AgentInitType initType;
}
