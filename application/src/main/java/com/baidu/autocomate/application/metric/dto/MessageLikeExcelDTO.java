package com.baidu.autocomate.application.metric.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ColumnWidth(30)
public class MessageLikeExcelDTO {
    @ExcelProperty("URL")
    private String url;
    @ExcelProperty("用户问题")
    private String question;
    @ExcelProperty("模型回答")
    private String answer;

    @ColumnWidth(10)
    @ExcelProperty("点赞点踩")
    private String likeStatus;

    @ColumnWidth(20)
    @ExcelProperty("消息创建时间")
    private String createTime;

    @ColumnWidth(20)
    @ExcelProperty("点赞点踩时间")
    private String likeTime;

    @ExcelProperty("代码仓库")
    private String repos;

    @ExcelProperty("搜索结果")
    private String searchResult;
}
