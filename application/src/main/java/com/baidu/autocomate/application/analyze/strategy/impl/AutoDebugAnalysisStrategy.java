package com.baidu.autocomate.application.analyze.strategy.impl;

import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.baidu.autocomate.domain.autodebug.bean.AutoDebugAnalysisRequest;
import com.baidu.autocomate.domain.autodebug.bean.AutoDebugAnalysisResponse;
import com.baidu.autocomate.domain.autodebug.service.AutoDebugService;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.search.bean.SearchStrategyBean;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class AutoDebugAnalysisStrategy implements AnalyzeStrategy {

    @Autowired
    private AutoDebugService autoDebugService;

    @Override
    public String getSlash() {
        return "Auto Debug";
    }

    @Override
    public AnalyzeResult analyze(AnalyzeCommand analyzeCommand) {
        List<AutoworkContext> contexts = analyzeCommand.getContexts();
        String repo = null;
        String cwd = null;
        String content = null;
        if (CollectionUtils.isNotEmpty(contexts)) {
            repo = contexts.stream()
                    .filter(Objects::nonNull)
                    .filter(context -> AutoworkContextType.REPO.equals(context.getType()))
                    .map(AutoworkContext::getName)
                    .filter(Objects::nonNull)
                    .findFirst().orElse(null);
            cwd = contexts.stream()
                    .filter(Objects::nonNull)
                    .filter(context -> AutoworkContextType.TERMINAL.equals(context.getType()))
                    .map(AutoworkContext::getPath)
                    .filter(Objects::nonNull)
                    .findFirst().orElse(null);
            content = contexts.stream()
                    .filter(Objects::nonNull)
                    .filter(context -> AutoworkContextType.TERMINAL.equals(context.getType()))
                    .map(AutoworkContext::getContent)
                    .filter(Objects::nonNull)
                    .findFirst().orElse(null);
        }
        AutoDebugAnalysisRequest request = AutoDebugAnalysisRequest.builder()
                .platform(analyzeCommand.getIde()).conversationId(analyzeCommand.getConversationId())
                .query(analyzeCommand.getQuery()).repo(repo).cwd(cwd).userName(analyzeCommand.getUserName())
                .contexts(content)
                .build();
        AutoDebugAnalysisResponse response = autoDebugService.analysis(request);
        return transferTo(analyzeCommand, response);
    }

    private AnalyzeResult transferTo(AnalyzeCommand command, AutoDebugAnalysisResponse response) {
        AutoDebugAnalysisResponse.AnalysisData data = response.getData();
        Map<String, Object> extend = new HashMap<>();
        if (data != null) {
            extend.put("recordId", data.getRecordId());
            extend.put("content", data.getRewriteQuery());
        }
        AutoDebugAnalysisResponse.FileContext context = response.getFileContext();
        if (context != null) {
            extend.put("context", context);
        }
        List<SearchStrategyBean> searchStrategyBeans = new ArrayList<>();
        for (AutoDebugAnalysisResponse.ClosestFunction closestFunction : response.getClosestFunctions()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(closestFunction), Map.class);
            params.put("customRule", "CLOSEST_FUNCTION");
            AutoworkContext autoworkContext = AutoworkContext.builder().id(closestFunction.getFilePath())
                    .name(closestFunction.getFilePath()).type(AutoworkContextType.FILE).params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }
        for (AutoDebugAnalysisResponse.Definition definition : response.getDefinitions()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(definition), Map.class);
            params.put("customRule", "DEFINITION");
            AutoworkContext autoworkContext = AutoworkContext.builder().id(definition.getFilePath())
                    .name(definition.getFilePath()).type(AutoworkContextType.FILE).params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }
        for (AutoDebugAnalysisResponse.File file : response.getFiles()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(file), Map.class);
            params.put("customRule", "FILE");
            AutoworkContext autoworkContext = AutoworkContext.builder().id(file.getFilePath())
                    .name(file.getFilePath()).type(AutoworkContextType.FILE).params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }
        for (AutoDebugAnalysisResponse.DirectoryTree directoryTree : response.getDirectoryTrees()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(directoryTree), Map.class);
            params.put("customRule", "DIRECTORY_TREE");
            AutoworkContext autoworkContext = AutoworkContext.builder().id(directoryTree.getFilePath())
                    .name(directoryTree.getFilePath()).type(AutoworkContextType.FOLDER).params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }
        for (AutoDebugAnalysisResponse.SymbolsInStructOrPackage symbolsInStructOrPackage :
                response.getSymbolsInStructOrPackage()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(symbolsInStructOrPackage), Map.class);
            params.put("customRule", "SYMBOLS_IN_STRUCT_OR_PACKAGE");
            AutoworkContext autoworkContext = AutoworkContext.builder().id(symbolsInStructOrPackage.getFilePath())
                    .name(symbolsInStructOrPackage.getFilePath()).type(AutoworkContextType.FOLDER).params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }

        for(AutoDebugAnalysisResponse.Mybatis mybatis : response.getMybatis()) {
            Map params = GsonConstants.GSON.fromJson(GsonConstants.GSON.toJson(mybatis), Map.class);
            params.put("customRule", "MYBATIS");
            AutoworkContext autoworkContext = AutoworkContext.builder().params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }

        if (context != null && context.getMetadata() != null) {
            Map<String, Object> params = Maps.newHashMapWithExpectedSize(2);
            params.put("metadata", context.getMetadata());
            params.put("customRule", "METADATA");
            AutoworkContext autoworkContext = AutoworkContext.builder().params(params).build();
            searchStrategyBeans.add(SearchStrategyBean.builder().rule("READ").context(autoworkContext).build());
        }
        AnalyzeResult analyzeResult = AnalyzeResult.builder().conversationId(command.getConversationId())
                .messageId(command.getMessageId()).isTech(true).queryType(response.getQueryType())
                .searchStrategies(searchStrategyBeans).build();
        analyzeResult.setExtend(extend);
        return analyzeResult;
    }
}
