package com.baidu.autocomate.application.question.bean;

import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AskRelatedQuestionRequest {

    @JsonProperty("contexts")
    private List<AutoworkContext> contexts = List.of();

    @JsonProperty("query")
    private String query;

    @JsonProperty("answer")
    private String answer = "";

    @JsonProperty("count")
    private Integer count = 3;
}
