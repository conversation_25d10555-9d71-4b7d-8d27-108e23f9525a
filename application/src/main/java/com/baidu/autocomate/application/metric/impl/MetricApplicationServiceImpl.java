package com.baidu.autocomate.application.metric.impl;

import com.baidu.autocomate.application.metric.MetricApplicationService;
import com.baidu.autocomate.application.metric.convertor.IdePasteMetricDTOConvertor;
import com.baidu.autocomate.application.metric.cqe.IdePasteMetricCommand;
import com.baidu.autocomate.application.metric.cqe.MessageLikeQuery;
import com.baidu.autocomate.application.metric.dto.MessageLikeExcelDTO;
import com.baidu.autocomate.application.metric.dto.RegisterConvertRateDTO;
import com.baidu.autocomate.application.metric.dto.RetenionMetricDTO;
import com.baidu.autocomate.domain.account.repos.AccountRepository;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.idepaste.entity.IdePasteMetricEntity;
import com.baidu.autocomate.domain.idepaste.repos.IdePasteMetricRepository;
import com.baidu.autocomate.domain.jobs.executor.code.bean.ComateWebContentDetail;
import com.baidu.autocomate.domain.knowledge.code.bean.WebCodeQAQueryBean;
import com.baidu.autocomate.domain.message.entity.MessageEntity;
import com.baidu.autocomate.domain.message.enums.LikeStatus;
import com.baidu.autocomate.domain.message.enums.MessageType;
import com.baidu.autocomate.domain.message.repos.MessageRepository;
import com.baidu.autocomate.domain.quota.repos.QuotaRepository;
import com.baidu.autocomate.domain.utils.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MetricApplicationServiceImpl implements MetricApplicationService {
    @Autowired
    private QuotaRepository quotaRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private MessageRepository messageRepository;
    @Autowired
    private IdePasteMetricRepository idePasteMetricRepository;

    @Autowired
    private IdePasteMetricDTOConvertor convertor;

    @Value("${comate.domain.uri:http://comate-external-test.now.baidu.com}")
    private String comateDomainUrl;
    @Override
    public RetenionMetricDTO getUserRetenion(LocalDateTime startDate, LocalDateTime endDate) {
        Long oneDayRetentionUsers = quotaRepository.userRetentionNum(startDate, endDate, 1);
        Long sevenDayRetentionUsers  = quotaRepository.userRetentionNum(startDate, endDate, 7);
        Long thirtyDayRetentionUsers = quotaRepository.userRetentionNum(startDate, endDate, 30);
        Long ninetyDayRetentionUsers = quotaRepository.userRetentionNum(startDate, endDate, 90);
        RetenionMetricDTO dto = RetenionMetricDTO.builder().oneDayRetentionUsers(oneDayRetentionUsers)
                .sevenDayRetentionUsers(sevenDayRetentionUsers)
                .thirtyDayRetentionUsers(thirtyDayRetentionUsers)
                .ninetyDayRetentionUsers(ninetyDayRetentionUsers).build();
        return dto;
    }

    @Override
    public RegisterConvertRateDTO getRegisterConvertRate(LocalDateTime startDate, LocalDateTime endDate) {
        Integer newRegisterNum = accountRepository.queryNewRegisterNum(startDate, endDate);
        Integer unLoginUserChatNum = accountRepository.unLoginChatUserNum(startDate, endDate);
        Double registerConvertRate = newRegisterNum + unLoginUserChatNum == 0 ? 0 :  newRegisterNum / (double) (newRegisterNum + unLoginUserChatNum);
        // 对 registerConvertRate 进行保留两位小数处理
        BigDecimal bd = new BigDecimal(Double.toString(registerConvertRate));
        Double roundedRate = bd.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        return RegisterConvertRateDTO.builder()
                .newRegisterNum(newRegisterNum.longValue())
                .unRegisterNum(unLoginUserChatNum.longValue())
                .rate(roundedRate)
                .build();
    }

    @Override
    public List<MessageLikeExcelDTO> getMessageLikeExcelDTOs(MessageLikeQuery query, LikeStatus likeStatus) {
        List<Long> conversationIds;
        if (likeStatus == null) {
            conversationIds = messageRepository.listConversationIdsByMessageTime(
                    DateUtils.getDayStartTimestamp(query.getStartDate()),
                    DateUtils.getDayEndTimestamp(query.getEndDate()));
        } else {
            conversationIds = messageRepository.likeOrUnLikeConversationIds(
                    DateUtils.getDayStartTimestamp(query.getStartDate()),
                    DateUtils.getDayEndTimestamp(query.getEndDate()),
                    likeStatus);
        }
        if (CollectionUtils.isEmpty(conversationIds)) {
            return Collections.emptyList();
        }
        Map<Long, List<MessageEntity>> conversation2MessageMap
                = messageRepository.getConversation2MessageMap(conversationIds);
        if (MapUtils.isEmpty(conversation2MessageMap)) {
            return Collections.emptyList();
        }
        SortedMap<Long, List<MessageEntity>> sortedMap = new TreeMap<>(conversation2MessageMap);
        return sortedMap.values().stream().map(this::to).flatMap(List::stream).collect(Collectors.toList());
    }

    List<MessageLikeExcelDTO> to(List<MessageEntity> messageEntities) {
        List<MessageLikeExcelDTO> dtos = Lists.newArrayList();
        MessageLikeExcelDTO dto = null;
        for (int i = 0; i < messageEntities.size(); i++) {
            String repo = "";
            String question = "";
            String answer = "";
            String searchResult = "";
            MessageEntity messageEntity = messageEntities.get(i);
            if (messageEntity.getSendType() == MessageType.USER) {
                if (messageEntity.getContent() != null) {
                    try {
                        WebCodeQAQueryBean queryBean = messageEntity.getContent().getDetail(WebCodeQAQueryBean.class);
                        if (queryBean != null) {
                            repo = GsonConstants.GSON.toJson(queryBean.getRepos());
                            question = queryBean.getQuery();
                        }
                    } catch (Exception e) {
                        log.error("getDetail error, message id {}, question detail: {}", messageEntity.getId(),
                                GsonConstants.GSON.toJson(messageEntity.getContent().getDetail()), e);
                    }
                }
                dto = MessageLikeExcelDTO.builder()
                        .question(truncateString(question))
                        .repos(repo)
                        .url(genUrl(messageEntity.getConversationId(), messageEntity.getWorkflowBuildId()))
                        .createTime(DateUtils.timestampToString(messageEntity.getTimestamp()))
                        .build();
                dtos.add(dto);
            }
            if (messageEntity.getSendType() == MessageType.ASSISTANT && dto != null) {
                if (messageEntity.getContent() != null) {
                    try {
                        ComateWebContentDetail comateWebContentDetail = messageEntity.getContent()
                                .getDetail(ComateWebContentDetail.class);
                        answer = comateWebContentDetail.getSummary();
                        searchResult = GsonConstants.GSON.toJson(comateWebContentDetail.getSearchResult());
                    } catch (Exception e) {
                        log.error("getDetail error, message id {}, answer detail: {}", messageEntity.getId(),
                                GsonConstants.GSON.toJson(messageEntity.getContent().getDetail()), e);
                    }
                }
                dto.setAnswer(truncateString(answer));
                dto.setLikeStatus(messageEntity.getLikeExcelString());
                dto.setLikeTime(DateUtils.timestampToString(messageEntity.getLikeTimestamp()));
                dto.setSearchResult(truncateString(searchResult));
                dto = null;
            }
        }
        return dtos;
    }


    public static String truncateString(String input) {
        // 设置允许的最大长度
        int maxLength = 32000;
        // 检查字符串是否为空或者已经在限制范围内
        if (input == null || input.length() <= maxLength) {
            // 返回原字符串
            return input;
        }
        // 如果超出长度限制，截取前maxLength个字符并添加"..."
        return input.substring(0, maxLength) + "...";
    }


    public String genUrl(Long conversationId, Long workflowBuildId) {

        return String.format("%s?conversationId=%s&workflowBuildId=%s",
                comateDomainUrl + "/zh/chat", encodeToBase64(conversationId.toString()),
                encodeToBase64(workflowBuildId.toString()));
    }

    @Override
    public void recordIdePasteMetric(IdePasteMetricCommand idePasteMetricCommand) {
        IdePasteMetricEntity entity = convertor.convert(idePasteMetricCommand);
        idePasteMetricRepository.save(entity);
    }

    private String encodeToBase64(String str) {
        return Base64.getEncoder().encodeToString(str.getBytes(java.nio.charset.StandardCharsets.UTF_8));
    }
}
