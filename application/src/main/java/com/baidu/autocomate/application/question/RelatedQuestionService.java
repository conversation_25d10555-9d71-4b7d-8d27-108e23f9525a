package com.baidu.autocomate.application.question;

import com.baidu.autocomate.application.question.bean.AskRelatedQuestionRequest;
import com.baidu.autocomate.application.question.bean.AskRelatedQuestionResponse;
import java.util.List;

public interface RelatedQuestionService {

    /**
     * 生成关联问题
     * @param user
     * @param relatedQuestionRequest
     * @return
     */
    List<AskRelatedQuestionResponse> askRelatedQuestions(String user, AskRelatedQuestionRequest relatedQuestionRequest);
}
