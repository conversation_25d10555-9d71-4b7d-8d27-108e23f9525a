package com.baidu.autocomate.application.yiyan.cqe;

import com.baidu.autocomate.application.common.advisor.Validation;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

@Data
@ToString
public class YiYanWebSearchQuery implements Validation {

    /**
     * 搜索的关键词
     */
    private String keywords;

    /**
     * 用户输入的代码仓库地址
     */
    private String repoId;

    /**
     * 用户输入的基于某个url进行搜索的地址
     */
    private String url;

    @Override
    public void validate() {
        Assert.isTrue(StringUtils.isNotBlank(keywords), "keywords can not be empty");
    }
}