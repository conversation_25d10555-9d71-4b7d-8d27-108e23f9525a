/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.strategy;

import javax.annotation.PostConstruct;

import com.baidu.autocomate.application.adopt.cqe.AdoptCommand;
import com.baidu.autocomate.application.adopt.factory.AdoptStrategyFactory;

public interface AdoptStrategy {

    @PostConstruct
    default void init() {
        AdoptStrategyFactory.registerAdoptStrategy(getSlash(), this);
    }

    String getSlash();

    void adopt(AdoptCommand adoptCommand);
}
