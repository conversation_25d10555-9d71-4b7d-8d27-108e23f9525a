package com.baidu.autocomate.application.question.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baidu.autocomate.application.question.RelatedQuestionService;
import com.baidu.autocomate.application.question.bean.AskRelatedQuestionRequest;
import com.baidu.autocomate.application.question.bean.AskRelatedQuestionResponse;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.prompts.PromptTemplate;
import com.baidu.autocomate.domain.llm.bean.CompletionContent;
import com.baidu.autocomate.domain.llm.bean.CompletionResult;
import com.baidu.autocomate.domain.llm.enums.LLMScene;
import com.baidu.autocomate.domain.llm.executor.LLMExecutor;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieAccountType;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieModelType;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieRequest;
import com.baidu.autocomate.domain.llm.model.erniecode.bean.ErnieCodeRequest;
import com.baidu.autocomate.domain.llm.utils.ParseUtils;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.shortcut.ShortcutPrompts;
import com.google.gson.reflect.TypeToken;
import com.theokanning.openai.completion.chat.ChatMessageRole;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RelatedQuestionServiceImpl implements RelatedQuestionService, LLMExecutor {

    private static final String CONTEXT_TEMPLATE = "|<%s>|";

    private static final String NOTICE_TEMPLATE = "\n# 注意\n" +
            "原始问题中存在 %s 相关的语法，这表示指定了原始问题的具体范围，" +
            "生成的相关问题也应包含此范围，即生成的问题中至少要包含一个范围。\n";

    @Value("${related.question.model:ERNIE_3_8K}")
    public String model;

    @Override
    public List<AskRelatedQuestionResponse> askRelatedQuestions(String user,
                                                                AskRelatedQuestionRequest request) {
        if (request == null || StringUtils.isBlank(request.getQuery())) {
            return Collections.emptyList();
        }

        try {
            // 1. 组装Prompt所需的contexts和query
            String contexts = "";
            String query = "";
            Map<String, AutoworkContext> contextMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(request.getContexts())) {
                for (AutoworkContext context : request.getContexts()) {
                    String formatContext = String.format(CONTEXT_TEMPLATE, context.getName());
                    contexts += formatContext + "、";
                    query = request.getQuery()
                            .replaceAll(context.getName(), formatContext);
                    contextMap.put(formatContext, context);
                }
                contexts = contexts.substring(0, contexts.length() - 1);
            }

            // 2. 获取生成的问题数量
            Integer count = request.getCount() <= 0 || request.getCount() >= 10 ? 3 : request.getCount();

            // 3. 组装Prompt
            String prompt = PromptTemplate.formatPrompt(ShortcutPrompts.getRelatedQuestionPrompt(),
                    Map.of("query", StringUtils.isBlank(query) ? request.getQuery() : query,
                            "answer", StringUtils.isBlank(request.getAnswer()) ? "" : request.getAnswer(),
                            "notice", StringUtils.isBlank(contexts) ? "" : String.format(NOTICE_TEMPLATE, contexts),
                            "count", String.valueOf(count)));

            // 4. 调用模型生成答案
            CompletionContent content = CompletionContent.builder()
                    .input(prompt)
                    .contentSource(MessageContentSource.MONGO)
                    .scene(LLMScene.RELATED_QUESTION)
                    .role(ChatMessageRole.USER)
                    .username(user)
                    .sessionId(null)
                    .model(model)
                    .build();
            CompletionResult result;
            if (ernieCodeRequest(model)) {
                result = chatCompletionWithErnieCode(content, ErnieCodeRequest.builder().build());
            } else {
                ErnieRequest ernieRequest = ErnieRequest.builder()
                        .ernieAccountType(ErnieAccountType.AUTO_WORK)
                        .ernieModelType(EnumUtils.getEnum(ErnieModelType.class, model, ErnieModelType.ERNIE_3_8K))
                        .useSingle(false)
                        .build();
                result = chatCompletionWithErnie(content, ernieRequest);
            }

            // 5. 解析模型结果，如未正常解析返回空
            List<AskRelatedQuestionResponse> questions = Collections.emptyList();
            List<String> queries = GsonConstants.GSON.fromJson(ParseUtils.parseJsonStr(result.getContent()),
                    new TypeToken<List<String>>() {
                    }.getType());
            log.info("generate related questions: {} by query: {}", request.getQuery(), queries);
            if (!CollectionUtils.isEmpty(queries)) {
                questions = queries.stream().map(q -> {
                    AskRelatedQuestionResponse question = new AskRelatedQuestionResponse();
                    question.setQuery(q);
                    return question;
                }).collect(Collectors.toList());
            }

            // 6. 设置contexts
            if (MapUtils.isNotEmpty(contextMap)) {
                for (AskRelatedQuestionResponse question : questions) {
                    contextMap.forEach((k, v) -> {
                        if (question.getQuery().contains(k)) {
                            question.setContext(v);
                        }
                    });
                }
            }

            return questions;
        } catch (Exception e) {
            log.error("exception message is:{} ", e.getMessage());
            return Collections.emptyList();
        }
    }
}
