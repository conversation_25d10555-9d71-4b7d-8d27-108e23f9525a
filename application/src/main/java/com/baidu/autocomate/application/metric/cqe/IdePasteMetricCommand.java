package com.baidu.autocomate.application.metric.cqe;

import lombok.Data;

@Data
public class IdePasteMetricCommand {

    @Data
    public static class Position {
        /**
         * 行号
         */
        private int line;
        /**
         * 列号
         */
        private int character;
    }

    /**
     * 文件路径
     */
    private String file;

    /**
     * 上下100行
     */
    private String content;

    /**
     * 编辑器
     */
    private String ide;

    /**
     * 用户粘贴的内容
     */
    private String pastedContent;

    /**
     * 用户粘贴时光标所在的行、列（坐标1）
     */
    private Position cursorPositionBeforePaste;

    /**
     * 粘贴完毕后光标所在的行、列（坐标2）
     */
    private Position cursorPositionAfterPaste;

    /**
     * 5s之后坐标1到坐标2区间内的代码变动
     */
    private String pastedContentAfter5Seconds;

    /**
     * 30s之后坐标1到坐标2区间内的代码变动
     */
    private String pastedContentAfter30Seconds;

    private Integer version;
}
