package com.baidu.autocomate.application.conversation.convert;

import com.baidu.autocomate.application.conversation.dto.ConversationMessageContentDTO;
import com.baidu.autocomate.application.conversation.dto.ConversationMessageDTO;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.message.beans.MessageSendBean;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring", imports = GsonConstants.class)
public interface ConversationDTOConvertor {
    @Mappings({
            @Mapping(source = "emitterId", target = "emitterId"),
            @Mapping(source = "interactionMode", target = "interactionMode"),
    })
    ConversationMessageDTO toConversationMessageDTO(MessageSendBean msBean);

    List<ConversationMessageDTO> toConversationMessageDTOList(List<MessageSendBean> msBeanList);


    @Mappings({
            @Mapping(target = "text", ignore = true)
    })
    ConversationMessageContentDTO toConversationMessageContentDTO(MessageContentEntity contentEntity);
}