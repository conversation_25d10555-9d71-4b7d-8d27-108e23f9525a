package com.baidu.autocomate.application.search.cqe;

import com.baidu.autocomate.application.common.advisor.Validation;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.baidu.autocomate.domain.i18n.MessageHolder;
import com.baidu.autocomate.domain.think.bean.SearchRule;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

@Data
public class SearchCommand implements Validation {
    /**
     * 会话ID
     */
    private Long conversationId;
    /**
     * 消息ID
     */
    private Long messageId;
    /**
     * 用户名或者License
     */
    private String userName;

    /**
     * 设备号,device用来鉴权用，传了使用 (userName+device双鉴权，任意一个通过即可) 不传依然使用userName鉴权
     */
    private String device;

    /**
     * 指令
     */
    private String slash;

    /**
     * 用户输入的Query
     */
    private String query;

    private String repoId;
    /**
     * 选择的上下文
     */
    private List<AutoworkContext> contexts;

    /**
     * 分析阶段的输出结果
     */
    private AnalyzeResult analyze;

    public List<AutoworkContext> getContexts() {
        if (contexts == null) {
            contexts = new ArrayList<>();
        }
        return contexts;
    }

    /**
     * 读取repoId，优先searchCommand.getRepoId，如果为空，则从context中读取type为REPO的
     *
     * @return repoId 可以为空
     */
    @Nullable
    public String getRepoId() {
        if (StringUtils.isNotBlank(this.repoId)) {
            return this.repoId;
        }

        if (CollectionUtils.isNotEmpty(this.contexts)) {
            // 目前只会有一个REPO context
            AutoworkContext repoContext = this.contexts.stream()
                    .filter(c -> AutoworkContextType.REPO.equals(c.getType()))
                    .findAny().orElse(null);
            return repoContext == null ? null : repoContext.getId();
        }
        return null;
    }

    /**
     * 判断是否只有一个CURRENT_FILE_READ的策略
     * <p>
     * 如果策略为空，或者策略的数量大于1，返回false
     * <p>
     * 否则，判断策略的rule是否为CURRENT_FILE_READ
     * </p>
     *
     * @return true if only one CURRENT_FILE_READ strategy exists, false otherwise
     */
    public boolean onlyHasCurrentFileStrategy() {
        List<com.baidu.autocomate.domain.think.bean.SearchStrategy> searchStrategies =
                (getAnalyze() == null) ? null : getAnalyze().getIntentStrategies();
        if (CollectionUtils.isEmpty(searchStrategies) || searchStrategies.size() > 1) {
            return false;
        }

        com.baidu.autocomate.domain.think.bean.SearchStrategy searchStrategy = searchStrategies.get(0);
        if (searchStrategy == null) {
            return false;
        }
        return SearchRule.CURRENT_FILE_READ == searchStrategy.getRule();
    }

    @Override
    public void validate() {
        Assert.notNull(conversationId, MessageHolder.get("param.is.null", "conversationId"));
        Assert.notNull(messageId, MessageHolder.get("param.is.null", "messageId"));
        Assert.notNull(userName, MessageHolder.get("param.is.null", "userName"));
        Assert.notNull(slash, MessageHolder.get("param.is.null", "slash"));
        Assert.notNull(analyze, MessageHolder.get("param.is.null", "analyze"));
    }
}
