package com.baidu.autocomate.application.workflow.bean;

import com.baidu.autocomate.domain.workflow.conf.WorkflowExecuteType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateWorkflowCommand {

    private Long agentId;

    private @NotNull String name;

    private @NotNull String description;

    private String slash;

    private WorkflowExecuteType executeType = WorkflowExecuteType.SERIAL_BLOCKING;

    private List<CreateJobCommand> jobs;
}
