package com.baidu.autocomate.application.analyze.strategy.composer;

import com.baidu.autocomate.application.quota.exception.ComposerUnauthorizedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;

@Slf4j
@Component
public class ComposerWhiteListChecker {
    
    private static final String WHITELIST_KEY = "composer:whitelist";

    /**
     * 一般不需要检查Composer白名单，现在只有SaaS环境需要检查
     */
    @Value("${composer.whiteList.enable:false}")
    private boolean enableWhiteList;

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    public void checkWhitelist(AnalyzeCommand command) {
        if (!enableWhiteList) {
            return;
        }

        String userName = command.getUserName();
        
        Boolean isInWhitelist = redisTemplate.opsForSet().isMember(WHITELIST_KEY, userName);
        
        if (BooleanUtils.isNotTrue(isInWhitelist)) {
            log.warn("Composer check white list, user {} is not in whitelist", userName);
            throw new ComposerUnauthorizedException("用户不在白名单");
        }

    }
}
