package com.baidu.autocomate.application.yiyan.utils;

import com.baidu.autocomate.application.yiyan.dto.YiYanActionContentDTO;
import com.baidu.autocomate.application.yiyan.dto.YiYanActionWebSearchDTO;
import com.baidu.autocomate.domain.searcher.result.WebSearchResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class YiYanUtils {

    /**
     *
     * @param searchResults
     * @return
     */
    public static YiYanActionContentDTO genYiYanActionContent(List<WebSearchResult> searchResults) {
        YiYanActionContentDTO content = new YiYanActionContentDTO();
        if (CollectionUtils.isEmpty(searchResults)) {
            return content;
        }
        Long subscriptId = NumberUtils.LONG_ZERO;
        Map<String, List<WebSearchResult>> map = new LinkedHashMap<>();
        for (WebSearchResult searchResult : searchResults) {
            List<WebSearchResult> list = map.getOrDefault(searchResult.getUrl(), new ArrayList<>());
            list.add(searchResult);
            map.put(searchResult.getUrl(), list);
        }
        List<YiYanActionWebSearchDTO> lines = new ArrayList<>();
        for (Map.Entry<String, List<WebSearchResult>> entry : map.entrySet()) {
            List<WebSearchResult> list = entry.getValue();
            list.sort(Comparator.comparingInt(WebSearchResult::getOrder));
            WebSearchResult first = list.get(0);
            YiYanActionWebSearchDTO line = YiYanActionWebSearchDTO.builder().number(++subscriptId)
                    .title(first.getTitle()).url(entry.getKey()).build();
            lines.add(line);
        }
        content.setWebList(lines);
        return content;
    }
}
