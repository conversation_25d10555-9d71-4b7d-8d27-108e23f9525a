package com.baidu.autocomate.application.conversation.dto;

import com.baidu.autocomate.domain.jobs.executor.code.bean.CodeQAMode;
import com.baidu.autocomate.domain.message.enums.LikeStatus;
import com.baidu.autocomate.domain.message.enums.MessageInteractionMode;
import com.baidu.autocomate.domain.message.enums.MessageType;
import lombok.Data;

import java.util.List;

@Data
public class ConversationMessageDTO {

    private Long messageId;

    private MessageType messageType;

    private Long conversationId;

    private Long workflowBuildId;

    private Long jobBuildId;

    private List<MessageTaskDTO> tasks;

    private ConversationMessageContentDTO content;

    private boolean cancelled;

    private Long timestamp;

    private String emitterId;

    private MessageInteractionMode interactionMode;

    /**
     * 消息点赞点踩状态
     */
    private LikeStatus likeStatus;
    
    private CodeQAMode mode;
}