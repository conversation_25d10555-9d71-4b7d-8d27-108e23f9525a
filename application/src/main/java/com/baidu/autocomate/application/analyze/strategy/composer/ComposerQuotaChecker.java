package com.baidu.autocomate.application.analyze.strategy.composer;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.quota.cqe.UserQuotaQuery;
import com.baidu.autocomate.application.quota.exception.ComposerLLMInsufficientException;
import com.baidu.autocomate.application.quota.exception.ComposerQuotaLimitException;
import com.baidu.autocomate.application.quota.exception.ComposerUnauthorizedException;
import com.baidu.autocomate.application.quota.service.QuotaApplicationService;
import com.baidu.autocomate.domain.llm.model.claude.ClaudeSKManager;
import com.baidu.autocomate.domain.quota.entity.QuotaConfigEntity;
import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import com.baidu.autocomate.domain.utils.EnvironmentUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
public class ComposerQuotaChecker {

    @Value("${org.service.endpoint:http://10.11.144.132:8030/org/key}")
    private String orgServiceEndpoint;

    @Autowired
    private QuotaApplicationService quotaApplicationService;

    @Autowired
    private ClaudeSKManager claudeSKManager;

    @Autowired
    private EnvironmentUtils environmentUtils;

    @Autowired
    private RestTemplate restTemplate;

    public void checkAndRecordUsage(AnalyzeCommand analyzeCommand) {
        String username = analyzeCommand.getUserName();

        if (isInternalEnv()) {
            // 厂内环境: 检查用户每日额度
            checkInternalQuota(username);
        } else {
            // SaaS环境: 检查用户总额度和企业总额度
            checkSaasQuota(username);
        }
    }

    private void checkSaasQuota(String username) {
        boolean hasAnyQuota = false;
        
        // 1. 检查用户总额度
        UserQuotaQuery userQuery = UserQuotaQuery.builder()
                .subjectType(SubjectType.USER)
                .subjectId(username)
                .resourceType(ResourceType.COMPOSER)
                .timeRange(TimeRange.TOTAL)
                .build();
        QuotaConfigEntity userConfig = quotaApplicationService.queryQuotaConfig(userQuery);

        if (userConfig != null) {
            hasAnyQuota = true;
            if (quotaApplicationService.queryUserQuotaLeft(userQuery, userConfig) <= 0) {
                log.info("User {} exceeded personal total quota limit", username);
                throw new ComposerQuotaLimitException();
            }
        }

        String orgId = getOrgId(username);
        // 2. 检查企业总额度
        if (StringUtils.isNoneBlank(orgId)) {
            UserQuotaQuery orgQuery = UserQuotaQuery.builder()
                    .subjectType(SubjectType.ORGANIZATION)
                    .subjectId(orgId)
                    .resourceType(ResourceType.COMPOSER)
                    .timeRange(TimeRange.TOTAL)
                    .build();
            QuotaConfigEntity orgConfig = quotaApplicationService.queryQuotaConfig(orgQuery);

            if (orgConfig != null) {
                hasAnyQuota = true;
                if (quotaApplicationService.queryUserQuotaLeft(orgQuery, orgConfig) <= 0) {
                    log.info("Organization {} exceeded total quota limit", orgId);
                    throw new ComposerQuotaLimitException();
                }
            }
        }

        // 3. 检查默认配额
        UserQuotaQuery defaultQuery = UserQuotaQuery.builder()
                .subjectType(SubjectType.DEFAULT)
                .resourceType(ResourceType.COMPOSER)
                .timeRange(TimeRange.TOTAL)
                .build();
        QuotaConfigEntity defaultConfig = quotaApplicationService.queryQuotaConfig(defaultQuery);

        if (defaultConfig != null) {
            hasAnyQuota = true;
            if (quotaApplicationService.queryUserQuotaLeft(defaultQuery, defaultConfig) <= 0) {
                log.info("User {} exceeded default total quota limit", username);
                throw new ComposerQuotaLimitException();
            }
        }

        // 如果没有任何配额,抛出异常
        if (!hasAnyQuota) {
            log.info("No quota config found for user {} and org {}", username, orgId);
            throw new ComposerUnauthorizedException();
        }

        // 所有配额检查都通过后,记录一次使用记录
        quotaApplicationService.recordUserQuota(username, ResourceType.COMPOSER);
    }

    private void checkInternalQuota(String username) {
        boolean hasAnyQuota = false;
        
        // 1. 检查用户每日额度
        UserQuotaQuery userQuery = UserQuotaQuery.builder()
                .subjectType(SubjectType.USER)
                .subjectId(username)
                .resourceType(ResourceType.COMPOSER)
                .timeRange(TimeRange.DAY)
                .build();
        QuotaConfigEntity userConfig = quotaApplicationService.queryQuotaConfig(userQuery);

        if (userConfig != null) {
            hasAnyQuota = true;
            if (quotaApplicationService.queryUserQuotaLeft(userQuery, userConfig) <= 0) {
                log.info("User {} exceeded daily Composer quota limit", username);
                throw new ComposerQuotaLimitException();
            }
        }

        // 2. 检查默认每日额度
        UserQuotaQuery defaultQuery = UserQuotaQuery.builder()
                .subjectType(SubjectType.DEFAULT)
                .resourceType(ResourceType.COMPOSER)
                .timeRange(TimeRange.DAY)
                .build();
        QuotaConfigEntity defaultConfig = quotaApplicationService.queryQuotaConfig(defaultQuery);

        if (defaultConfig != null) {
            hasAnyQuota = true;
            if (quotaApplicationService.queryUserQuotaLeft(defaultQuery, defaultConfig) <= 0) {
                log.info("User {} exceeded default daily quota limit", username);
                throw new ComposerQuotaLimitException();
            }
        }

        // 如果没有任何配额,抛出异常
        if (!hasAnyQuota) {
            log.info("No quota config found for user {}", username);
            throw new ComposerUnauthorizedException();
        }

        // 所有配额检查都通过后,记录一次使用记录
        quotaApplicationService.recordUserQuota(username, ResourceType.COMPOSER);
    }

    private boolean isInternalEnv() {
        return environmentUtils.isInternal();
    }

    private String getOrgId(String username) {
        if (StringUtils.isBlank(username)) {
            log.warn("Username is blank");
            return null;
        }

        try {
            String url = orgServiceEndpoint + "/" + username;
            OrgResponse response = restTemplate.getForObject(url, OrgResponse.class);
            if (response == null) {
                log.warn("get orgId response is null for username: {}", username);
                return null;
            }
            
            if (!"OK".equals(response.getStatus())) {
                log.warn("get orgId response status is not OK for username: {}, status: {}, message: {}", 
                    username, response.getStatus(), response.getMessage());
                return null;
            }
            
            if (response.getData() == null) {
                log.warn("get orgId response data is null for username: {}", username);
                return null;
            }

            return response.getData().getOrgId();
        } catch (Exception e) {
            log.error("Failed to get orgId for username: {}, error: {}", username, e.getMessage(), e);
            return null;
        }
    }

    @Data
    static class OrgResponse {
        private String status;
        private String message;
        private OrgData data;
    }

    @Data
    static class OrgData {
        private String orgId;
    }
} 