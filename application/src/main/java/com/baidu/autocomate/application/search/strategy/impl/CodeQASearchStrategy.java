package com.baidu.autocomate.application.search.strategy.impl;

import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.baidu.autocomate.domain.knowledge.code.bean.KnowledgeQueryBean;
import com.baidu.autocomate.domain.knowledge.code.bean.KnowledgeSearchRequest;
import com.baidu.autocomate.domain.knowledge.code.bean.Repo;
import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.searcher.DataSearchHelper;
import com.baidu.autocomate.domain.searcher.SearchType;
import com.baidu.autocomate.domain.searcher.param.CodeGraphSearchParam;
import com.baidu.autocomate.domain.searcher.param.CodeSearchParam;
import com.baidu.autocomate.domain.searcher.param.KnowledgeSearchParam;
import com.baidu.autocomate.domain.searcher.param.RepoMetaSearchParam;
import com.baidu.autocomate.domain.searcher.param.SearchParam;
import com.baidu.autocomate.domain.searcher.param.SearchParams;
import com.baidu.autocomate.domain.searcher.param.WebSearchParam;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import com.baidu.autocomate.domain.searcher.searcher.code.RepoMetaType;
import com.baidu.autocomate.domain.think.bean.SearchRule;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.baidu.autocomate.domain.searcher.searcher.code.EmbedddingCodeRetrieval.COMATE_WEB_LICENSE;

@Component
@Slf4j
public class CodeQASearchStrategy implements SearchStrategy {
    @Autowired
    private DataSearchHelper dataSearchHelper;

    @Value("${knowledge.topK:20}")
    private Integer knowledgeTopK;

    @Override
    public String getSlash() {
        return Slash.CODE_QA.getName();
    }

    @Override
    public DataSearchResult search(SearchCommand searchCommand) {
        if (searchCommand.getAnalyze() != null
                && CollectionUtils.isNotEmpty(searchCommand.getAnalyze().getIntentStrategies())) {
            // 新的检索逻辑
            return searchUseIntentStrategies(searchCommand);
        }
        return searchUseContexts(searchCommand);
    }

    /**
     * 根据传入的SearchCommand对象进行上下文搜索
     *
     * @param searchCommand 包含搜索所需信息的SearchCommand对象
     * @return DataSearchResult 搜索结果
     */
    private DataSearchResult searchUseContexts(SearchCommand searchCommand) {
        // 构建搜索参数
        List<String> paths = new ArrayList<>();
        List<AutoworkContext> contexts = searchCommand.getContexts();
        // 构建repo参数
        String repoId = searchCommand.getRepoId();
        final List<Repo> repos = Lists.newArrayList(Repo.builder().repo(repoId).repoId(repoId).build());
        boolean needCodeSearch = false;
        // 构建知识搜索参数
        final List<KnowledgeQueryBean> knowledgeLocalCodeQueryBeans = Lists.newArrayList();
        final List<KnowledgeQueryBean> knowledgeTextQueryBeans = Lists.newArrayList();
        // 构建URL抓取的搜索参数
        final List<String> urlKnowledgeUUIDs = Lists.newArrayList();
        final List<String> readUUIDs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contexts)) {
            // 要检索的目录和文件范围
            contexts.stream().filter(c -> AutoworkContextType.FILE.equals(c.getType())
                            || AutoworkContextType.FOLDER.equals(c.getType()))
                    .forEach(c -> paths.add(c.getId()));
            boolean hasRepoId = contexts.stream().anyMatch(c -> AutoworkContextType.REPO.equals(c.getType()));
            // 如果选择了代码库，或者文件、目录范围，则进行代码检索
            if (hasRepoId || CollectionUtils.isNotEmpty(paths)) {
                needCodeSearch = true;
            }

            // 构建查询系统和普通知识集以及iAPI项目对应的知识集的搜索参数
            contexts.stream().filter(c -> AutoworkContextType.SYSTEM.equals(c.getType())
                            || AutoworkContextType.NORMAL.equals(c.getType())
                            || AutoworkContextType.PERSONAL.equals(c.getType())
                            || AutoworkContextType.DEPARTMENT.equals(c.getType())
                            || AutoworkContextType.API_PROJECT.equals(c.getType()))
                    .forEach(c -> {
                        String type = c.getType().name();
                        if (AutoworkContextType.API_PROJECT.equals(c.getType())) {
                            type = "IAPI";
                        }
                        KnowledgeQueryBean knowledgeQueryBean = KnowledgeQueryBean.builder()
                                .type(type).uuid(c.getId()).build();
                        if (KnowledgeSearchRequest.KnowledgeRetrievalType.LOCAL_CODE.equals(c.getRetrievalType())) {
                            knowledgeLocalCodeQueryBeans.add(knowledgeQueryBean);
                        } else {
                            knowledgeTextQueryBeans.add(knowledgeQueryBean);
                        }
                    });

            // URL抓取的知识、或者单个API的知识
            contexts.stream().filter(c -> AutoworkContextType.URL.equals(c.getType())
                            || AutoworkContextType.API.equals(c.getType()))
                    .forEach(c -> urlKnowledgeUUIDs.add(c.getId()));
            // 纯网页的
            contexts.stream().filter(c -> AutoworkContextType.URL.equals(c.getType()))
                    .forEach(c -> readUUIDs.add(c.getId()));
        }
        // 构建网络搜索参数
        String webQuery = "";
        if (CollectionUtils.isNotEmpty(contexts)) {
            webQuery = contexts.stream().filter(c -> AutoworkContextType.WEB.equals(c.getType()))
                    .map(AutoworkContext::getContextQuery)
                    .findFirst()
                    .orElse(null);
        }
        CodeSearchParam codeEmbeddingSearchParam = CodeSearchParam.builder()
                .license(searchCommand.getUserName())
                .repos(repos)
                .subQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                .paths(paths).build();
        // 特殊处理github库的情况，为了支持在IDE端的推荐用例
        if (needCodeSearch && StringUtils.isNotBlank(repoId) && repoId.startsWith("github")) {
            codeEmbeddingSearchParam.setFillContent(true);
            codeEmbeddingSearchParam.setLicense(COMATE_WEB_LICENSE);
            // 代码检索类型是向量检索
            codeEmbeddingSearchParam.setCodeSearchType(CodeSearchParam.CodeSearchType.VECTOR);
        }

        List<SearchType> searchTypes = new ArrayList<>();
        Map<SearchType, List<SearchParam>> searchParamMap = Maps.newHashMapWithExpectedSize(2);
        if (needCodeSearch) {
            searchParamMap.put(SearchType.CODE, Lists.newArrayList(codeEmbeddingSearchParam));
            searchTypes.add(SearchType.CODE);
        }
        if (CollectionUtils.isNotEmpty(knowledgeTextQueryBeans)
                || CollectionUtils.isNotEmpty(urlKnowledgeUUIDs)) {
            SearchParam knowledgeTextEmbeddingSearchParam = KnowledgeSearchParam.builder()
                    .source("COMATE")
                    .knowledgeRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT)
                    .knowledgeQueryBeans(knowledgeTextQueryBeans)
                    .knowledgeUUIDs(urlKnowledgeUUIDs)
                    .readUUIDs(readUUIDs)
                    .additionalQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                    .build();
            searchParamMap.put(SearchType.KNOWLEDGE, Lists.newArrayList(knowledgeTextEmbeddingSearchParam));
            searchTypes.add(SearchType.KNOWLEDGE);
        }

        if (CollectionUtils.isNotEmpty(knowledgeLocalCodeQueryBeans)) {
            SearchParam knowledgeLocalCodeEmbeddingSearchParam = KnowledgeSearchParam.builder()
                    .source("COMATE")
                    .knowledgeRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.LOCAL_CODE)
                    .knowledgeQueryBeans(knowledgeLocalCodeQueryBeans)
                    .additionalQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                    .build();
            searchParamMap.put(SearchType.KNOWLEDGE, Lists.newArrayList(knowledgeLocalCodeEmbeddingSearchParam));
            searchTypes.add(SearchType.KNOWLEDGE);
        }
        if (StringUtils.isNotBlank(webQuery)) {
            WebSearchParam webSearchParam = WebSearchParam.builder().webQuery(webQuery).build();
            searchParamMap.put(SearchType.WEB, Lists.newArrayList(webSearchParam));
            searchTypes.add(SearchType.WEB);
        }
        SearchParams searchParams = SearchParams.builder()
                .searchTypes(searchTypes)
                .username(searchCommand.getUserName())
                .query(searchCommand.getQuery())
                .searchParamMap(searchParamMap)
                .device(searchCommand.getDevice())
                .build();
        DataSearchResult dataSearchResult = dataSearchHelper.search(searchParams);
        return dataSearchResult;
    }

    /**
     * 根据SearchCommand搜索使用intent strategies。
     *
     * @param searchCommand SearchCommand对象，包含搜索命令所需信息
     * @return DataSearchResult对象，表示搜索结果
     */
    private DataSearchResult searchUseIntentStrategies(SearchCommand searchCommand) {
        // 遍历intent strategies，转为SearchParam
        String repoId = searchCommand.getRepoId();
        Repo repo = Repo.builder().repoId(repoId).repo(repoId).build();
        SearchParams searchParams = SearchParams.builder()
                .username(searchCommand.getUserName())
                .device(searchCommand.getDevice())
                .query(searchCommand.getQuery())
                .build();
        List<RepoMetaType> repoMetaTypes = new ArrayList<>();
        searchCommand.getAnalyze().getIntentStrategies().stream()
                .filter(Objects::nonNull)
                .filter(strategy -> strategy.getRule() != null)
                .filter(strategy -> strategy.getContext() != null)
                .forEach(strategy -> {
                    SearchRule rule = strategy.getRule();
                    switch (rule) {
                        case REPO_VECTOR -> {
                            CodeSearchParam searchParam = CodeSearchParam.builder()
                                    .repos(Lists.newArrayList(repo))
                                    .license(searchCommand.getUserName())
                                    .codeSearchType(CodeSearchParam.CodeSearchType.VECTOR)
                                    .embeddingQuery(strategy.getQuery())
                                    .subQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                                    .build();
                            // 特殊处理github库的情况，为了支持在IDE端的推荐用例
                            if (StringUtils.isNotBlank(repoId) && repoId.startsWith("github")) {
                                searchParam.setFillContent(true);
                                searchParam.setLicense(COMATE_WEB_LICENSE);
                                // 代码检索类型是向量检索
                                searchParam.setCodeSearchType(CodeSearchParam.CodeSearchType.VECTOR);
                            }
                            searchParams.addSearchType(SearchType.CODE);
                            searchParams.addSearchParam(SearchType.CODE, searchParam);
                        }
                        case FOLDER_VECTOR -> {
                            CodeSearchParam searchParam = CodeSearchParam.builder()
                                    .repos(Lists.newArrayList(repo))
                                    .license(searchCommand.getUserName())
                                    .paths(Lists.newArrayList(strategy.getContext().getId()))
                                    .codeSearchType(CodeSearchParam.CodeSearchType.VECTOR)
                                    .embeddingQuery(strategy.getQuery())
                                    .subQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                                    .build();
                            searchParams.addSearchType(SearchType.CODE);
                            searchParams.addSearchParam(SearchType.CODE, searchParam);
                        }
                        case URL_VECTOR -> {
                            // 将URL_VECTOR认为是网页读取，而不是检索
                            KnowledgeSearchParam searchParam = KnowledgeSearchParam.builder()
                                    .readUUIDs(Lists.newArrayList(strategy.getContext().getId()))
                                    .knowledgeRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT)
                                    .build();
                            searchParams.addSearchType(SearchType.KNOWLEDGE);
                            searchParams.addSearchParam(SearchType.KNOWLEDGE, searchParam);
                        }
                        case WEB -> {
                            WebSearchParam searchParam = WebSearchParam.builder()
                                    .webQuery(strategy.getQuery())
                                    .build();
                            searchParams.addSearchType(SearchType.WEB);
                            searchParams.addSearchParam(SearchType.WEB, searchParam);
                        }
                        // DOC_VECTOR：基于知识集信息提问
                        case DOC_VECTOR, API_VECTOR -> {
                            if (strategy.getContext().getType() == null) {
                                log.error("invalid context type, strategy: {}", strategy);
                                return;
                            }
                            KnowledgeQueryBean knowledgeQueryBean = KnowledgeQueryBean.builder()
                                    .type(strategy.getContext().getType().name())
                                    .uuid(strategy.getContext().getId())
                                    .build();
                            // 知识的兜底策略，兜底的检索类型是TEXT
                            if (strategy.getContext().getRetrievalType() == null) {
                                strategy.getContext().setRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT);
                            }
                            KnowledgeSearchParam searchParam = KnowledgeSearchParam.builder()
                                    .knowledgeRetrievalType(strategy.getContext().getRetrievalType())
                                    .knowledgeQueryBeans(Lists.newArrayList(knowledgeQueryBean))
                                    .topK(knowledgeTopK)
                                    .build();
                            searchParams.addSearchType(SearchType.KNOWLEDGE);
                            searchParams.addSearchParam(SearchType.KNOWLEDGE, searchParam);
                        }
                        case API_READ, URL_READ -> {
                            KnowledgeSearchParam searchParam = KnowledgeSearchParam.builder()
                                    .knowledgeUUIDs(Lists.newArrayList(strategy.getContext().getId()))
                                    .readUUIDs(Lists.newArrayList(strategy.getContext().getId()))
                                    .knowledgeRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT)
                                    .build();
                            searchParams.addSearchType(SearchType.KNOWLEDGE);
                            searchParams.addSearchParam(SearchType.KNOWLEDGE, searchParam);
                        }
                        case DEPENDENT_READ -> {
                            repoMetaTypes.add(RepoMetaType.DEPENDENCY);
                        }
                        case REPO_API_LIST -> {
                            repoMetaTypes.add(RepoMetaType.API);
                        }
                        case SUMMARY_READ -> {
                            repoMetaTypes.add(RepoMetaType.FILE_TREE);
                            repoMetaTypes.add(RepoMetaType.README_SUMMARY);
                        }
                        case ARCHITECTURE_READ -> {
                            repoMetaTypes.add(RepoMetaType.FILE_TREE);
                            repoMetaTypes.add(RepoMetaType.REPO_SUMMARY);
                        }
                        case FILE_GRAPH -> {
                            CodeGraphSearchParam graphSearchParam = CodeGraphSearchParam.builder()
                                    .repoId(searchCommand.getRepoId())
                                    .filePath(strategy.getContext().getId())
                                    .cursorLine(strategy.getCursorLine())
                                    .build();
                            searchParams.addSearchType(SearchType.CODE_GRAPH);
                            searchParams.addSearchParam(SearchType.CODE_GRAPH, graphSearchParam);
                        }
                        default -> {
                            if (!searchParams.isEmpty() || StringUtils.isBlank(strategy.getQuery())) {
                                return;
                            }
                            if (searchCommand.onlyHasCurrentFileStrategy()) {
                                // 只有当前文件上下文时，也不需要检索
                                return;
                            }
                            // 对于所有没有处理的rule进行兜底
                            CodeSearchParam searchParam = CodeSearchParam.builder()
                                    .repos(Lists.newArrayList(repo))
                                    .license(searchCommand.getUserName())
                                    .codeSearchType(CodeSearchParam.CodeSearchType.VECTOR)
                                    .embeddingQuery(strategy.getQuery())
                                    .subQueries(searchCommand.getAnalyze().getCodeSearchQueries())
                                    .build();
                            searchParams.addSearchType(SearchType.CODE);
                            searchParams.addSearchParam(SearchType.CODE, searchParam);
                        }
                    }
                });

        if (!repoMetaTypes.isEmpty()) {
            repoMetaTypes.add(RepoMetaType.LANG_SUMMARY);
            RepoMetaSearchParam searchParam = RepoMetaSearchParam.builder()
                    .types(repoMetaTypes)
                    .repoId(repoId).build();
            searchParams.addSearchType(SearchType.REPO_META);
            searchParams.addSearchParam(SearchType.REPO_META, searchParam);
        }

        DataSearchResult dataSearchResult = dataSearchHelper.search(searchParams);
        return dataSearchResult;
    }
}
