package com.baidu.autocomate.application.metric.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.baidu.autocomate.application.metric.dto.MessageLikeExcelDTO;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class MergeUrlStrategy extends AbstractMergeStrategy {
    private final List<MessageLikeExcelDTO> data;
    private int startIndex = 0;
    private int lastIndex = 0;

    public MergeUrlStrategy(List<MessageLikeExcelDTO> data) {
        this.data = data;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer rowIndex) {
        if (head.getColumnIndex() == 0 && rowIndex != null) {
            if (rowIndex != 0) {
                String currentUrl = cell.getStringCellValue();
                String prevUrl = data.get(rowIndex - 1).getUrl();
                if (currentUrl.equals(prevUrl)) {
                    // 当前行和前一行的 url 值相同, 更新区域的终点
                    lastIndex = rowIndex;
                } else {
                    // 当前行和前一行的 url 值不同
                    if (startIndex != lastIndex) {
                        // 有相同的 url 序列存在，执行合并操作
                        sheet.addMergedRegionUnsafe(new CellRangeAddress(startIndex + 1, lastIndex + 1,
                                0, 0));
                    }
                    // 重置区域起点
                    startIndex = rowIndex;
                    lastIndex = rowIndex;
                }
                // 当处理到数据的最后一行时，检查并进行最后的合并操作
                if (rowIndex == data.size() - 1 && startIndex != lastIndex) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(startIndex + 1, lastIndex + 1, 0, 0));
                }
            } else {
                startIndex = 0;
            }
        }
    }
}