/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.analyze.strategy.impl;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


import com.baidu.autocomate.application.analyze.bean.ApiIntentionResult;
import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.rewrite.IntelliTestQueryRewrite;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import com.baidu.autocomate.domain.jobs.executor.code.QueryUtil;
import com.baidu.autocomate.domain.llm.model.iapi.bean.ApiIntention;
import com.baidu.autocomate.domain.utils.TraceUtils;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
@Component
public class ApiCodeAnalysisStrategy implements AnalyzeStrategy {


    @Value("${iapi.intention.url}")
    private String intentionUrl;

    @Autowired
    private QueryUtil queryUtil;

    @Autowired
    private IntelliTestQueryRewrite intelliTestQueryRewrite;

    @Override
    public String getSlash() {
        return Slash.API_CODE.getName();
    }

    @Override
    public AnalyzeResult analyze(AnalyzeCommand analyzeCommand) {

        AnalyzeResult analyzeResult = new AnalyzeResult();

        // 获取意图
        ApiIntention apiIntention = intentionAnalyze(analyzeCommand.getQuery(), analyzeCommand.getSelection());

        // 添加到extend
        analyzeResult.setExtend(convertToExtend(apiIntention));

        List<String> queries = null;
        if (analyzeCommand.isNeedQueryRewrite()) {
            if (apiIntention != null) {
                queries = apiIntention.getKeywords();
            } else {
                // 意图返回为空，走默认改写策略
                queries = queryUtil.rewriteQuery(null, analyzeCommand.getQuery(), analyzeCommand.getUserName());
            }
        }

        analyzeResult.setCodeSearchQueries(queries);
        // TODO 分析queryType
        analyzeResult.setQueryType(null);
        return analyzeResult;
    }

    /**
     * 获取意图
     * @param query
     * @param selection
     * @return
     */
    public ApiIntention intentionAnalyze(String query, String selection){

        Map<String, String> body = new HashMap<>();
        body.put("query", query);
        body.put("selection", selection);
        Request.Builder builder = new Request.Builder()
                .url(intentionUrl)
                .addHeader(TraceUtils.TRACE_ID, MDC.get(TraceUtils.TRACE_ID))
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(body)));

        Request request = builder.build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("IAPI intention analyze error, response:{}", response);
                return null;
            }
            String responseJson = response.body().string();
            log.info("IAPI intention analyze: {}", responseJson);
            ApiIntentionResult result = GSON.fromJson(responseJson, ApiIntentionResult.class);
            if (HttpStatus.SC_OK == result.getCode()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("IAPI intention analyze failed by params: {}", GsonConstants.GSON.toJson(request), e);
        }
        return null;
    }

    /**
     * 转换成extend
     * @param apiIntention
     * @return
     */
    public Map<String, Object> convertToExtend(ApiIntention apiIntention){
        if (apiIntention == null){
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(apiIntention, Map.class);
    }

}
