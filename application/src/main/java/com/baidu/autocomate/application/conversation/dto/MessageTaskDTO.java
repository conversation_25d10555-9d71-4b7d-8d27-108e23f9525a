package com.baidu.autocomate.application.conversation.dto;

import com.baidu.autocomate.domain.message.enums.TaskSource;
import com.baidu.autocomate.domain.message.enums.TaskStatus;
import com.baidu.autocomate.domain.message.enums.TaskType;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class MessageTaskDTO {

    private Long id;

    private Long messageId;

    private String desc;

    private TaskStatus status;

    private TaskType taskType;

    private TaskSource source;

    private Boolean showDetail = false;

    private List<String> detail = Collections.emptyList();
}