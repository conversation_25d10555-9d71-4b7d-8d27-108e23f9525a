package com.baidu.autocomate.application.permission.impl;

import com.baidu.autocomate.application.permission.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    @Value("#{'${admin.users:dW5sb2dpbl82MTU4NWE5Ny1iZWQwLTRiNDgtYTRlZi04ZDAzOGYxYWY1NDU=}'.split(',')}")
    private List<String> systemManagerUsers;

    @Override
    public boolean isSystemManager(String user) {
        return systemManagerUsers.contains(user);
    }
}
