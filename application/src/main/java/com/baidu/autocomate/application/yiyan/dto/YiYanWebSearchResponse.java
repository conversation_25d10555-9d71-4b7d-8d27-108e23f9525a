package com.baidu.autocomate.application.yiyan.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class YiYanWebSearchResponse {

    /**
     * prompt字段将会作为模型的回复规范进入大模型
     */
    private String prompt;

    /**
     * 平台约定字段，无需在 yaml 文件中定义仅支持数字和字母，不超过 10 个字符
     * 返回此字段不为0时，系统将认为api调用失败
     */
    private int errCode = 0;

    /**
     * 仅支持中文汉字，不超过 8 个字符，建议使用 4 个字符，且各动作名称要简单概要
     * 2_1是与一言那边对的，写死的
     */
    @Builder.Default
    private String actionName = "2_1";
    /**
     * 仅支持中文汉字，不超过 50 个字符，建议尽量简短可描述执行动作的内容、状态、错误信息等
     * 这样的数据格式
     * {
     *   "webList": [
     *     {
     *       "title": "1",
     *       "url": "https://zhuanlan.zhihu.com/p/665219418"
     *     },
     *     {
     *       "title": "2",
     *       "url": "https://zhuanlan.zhihu.com/p/665219418"
     *     },
     *     {
     *       "title": "3",
     *       "url": "https://zhuanlan.zhihu.com/p/665219418"
     *     }
     *   ]
     * }
     */
    private String searchResultForDisplay;

    /**
     * todo 是不是得包含url、title、content、id、等信息，是个json。 还是说返回一个可以直接放到prompt的字符串
     * 暂定使用搜索结果，格式参考： CodePrompt.WEB_RESULT_PROMPT_SINGLE_CHUNK
     */
    private String techSearchResult;
}
