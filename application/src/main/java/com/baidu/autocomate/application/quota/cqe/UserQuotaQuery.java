package com.baidu.autocomate.application.quota.cqe;

import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserQuotaQuery {

    @Builder.Default
    private SubjectType subjectType = SubjectType.DEFAULT;

    private String subjectId;

    private ResourceType resourceType;

    /**
     * 默认按照全时间范围查询
     */
    @Builder.Default
    private TimeRange timeRange = TimeRange.TOTAL;
}