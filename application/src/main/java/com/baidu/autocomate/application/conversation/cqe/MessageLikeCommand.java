package com.baidu.autocomate.application.conversation.cqe;

import com.baidu.autocomate.domain.message.enums.LikeStatus;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

@Data
@Validated
public class MessageLikeCommand {
    private String user;
    private Long conversationId;
    @NotNull(message = "messageId can not be null")
    private Long messageId;
    private LikeStatus likeStatus;

    /**
     * 点踩的时候可能需要给个反馈，或者以后点赞点踩都可以给反馈。只要传了 后端就记下来
     */
    private String feedBack;
}