package com.baidu.autocomate.application.cache;

import com.baidu.autocomate.domain.cache.Cache;
import com.baidu.autocomate.domain.cache.CacheData;
import com.baidu.autocomate.domain.cache.CacheKey;
import com.baidu.autocomate.domain.cache.CacheValue;
import com.baidu.autocomate.domain.cache.DataType;
import com.baidu.autocomate.domain.cache.factory.CacheConfig;
import com.baidu.autocomate.domain.cache.factory.CacheFactory;

public class CacheTester {
    
    public String cacheTest() {
        CacheConfig cacheConfig = new CacheConfig();
        cacheConfig.setCacheName("cache_test");
        CacheConfig.DataManagerConfig dataManagerConfig = new CacheConfig.DataManagerConfig();
        dataManagerConfig.setSimilarSearch(true);
        cacheConfig.setDataManagerConfig(dataManagerConfig);
        Cache cache = CacheFactory.buildCache(cacheConfig);
        // put
        CacheData data = new CacheData();
        CacheKey cacheKey = new CacheKey(DataType.STR, "product:iCafe,issue:项目列表新增我参与的");
        CacheValue cacheValue = new CacheValue(DataType.STR, "项目列表");
        cache.put(new CacheData(cacheKey, cacheValue));
        
        // search
        CacheData res = cache.search(new CacheKey(DataType.STR, "product:iCafe,issue:项目列表页新增我参与的tab"));
        return res.getCacheValue().getData().toString();
    }
    
}
