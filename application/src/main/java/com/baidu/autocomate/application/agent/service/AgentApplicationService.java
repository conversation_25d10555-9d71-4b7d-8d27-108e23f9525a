package com.baidu.autocomate.application.agent.service;

import com.baidu.autocomate.application.agent.bean.AgentAllConf;
import com.baidu.autocomate.application.agent.bean.CreateAgentCommand;
import com.baidu.autocomate.application.agent.bean.UpdateAgentCommand;

public interface AgentApplicationService {

    AgentAllConf create(CreateAgentCommand command);

    AgentAllConf update(UpdateAgentCommand command);

    AgentAllConf get(Long id);

    void delete(Long id);
}
