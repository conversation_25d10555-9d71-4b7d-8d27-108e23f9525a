package com.baidu.autocomate.application.metric.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RegisterConvertRateDTO {

    /**
     * 在这段时间范围内，有过使用记录(输入框内发送了query) 的Comate Web端未登录的去重用户数，
     * 必须保证这些用户在查询时间范围内是没有绑定过登录用户的
     */
    private Long unRegisterNum;

    /**
     * 在这段时间范围内，从Comate Web页面点击注册（登录）按钮后新增（之前Comate全域都未注册过）的用户数
     */
    private Long newRegisterNum;

    /**
     * 注册转化率, 也就是 newRegisterNum / (unRegisterNum + unRegisterNum)
     */
    private Double rate;
}
