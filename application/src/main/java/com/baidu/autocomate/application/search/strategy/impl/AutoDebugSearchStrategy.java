package com.baidu.autocomate.application.search.strategy.impl;

import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.autodebug.bean.AutoDebugAnalysisResponse;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AutoDebugSearchStrategy implements SearchStrategy {
    @Override
    public String getSlash() {
        return "Auto Debug";
    }

    @Override
    public DataSearchResult search(SearchCommand searchCommand) {
        AnalyzeResult analyze = searchCommand.getAnalyze();
        Map<String, Object> extend = analyze.getExtend();
        Map<String, Object> context = (Map<String, Object>) extend.get("context");
        AutoDebugAnalysisResponse.FileContext fileContext = GsonConstants.GSON.fromJson(
                GsonConstants.GSON.toJson(extend.get("context")), AutoDebugAnalysisResponse.FileContext.class);
        return null;
    }
}
