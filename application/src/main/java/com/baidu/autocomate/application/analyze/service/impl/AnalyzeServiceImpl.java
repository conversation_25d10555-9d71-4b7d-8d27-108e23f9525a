package com.baidu.autocomate.application.analyze.service.impl;

import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.application.analyze.service.AnalyzeService;
import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.factory.AnalyzeStrategyFactory;
import com.baidu.autocomate.application.analyze.strategy.AnalyzeStrategy;
import com.baidu.autocomate.application.conversation.cqe.AutoworkConversationCreateCommand;
import com.baidu.autocomate.application.conversation.service.AutoworkConversationService;
import com.baidu.autocomate.domain.conversation.entity.ConversationEntity;
import com.baidu.autocomate.domain.conversation.repos.ConversationRepository;
import com.baidu.autocomate.domain.i18n.MessageHolder;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import com.baidu.autocomate.domain.message.entity.MessageEntity;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.message.enums.MessageContentType;
import com.baidu.autocomate.domain.message.enums.MessageInteractionMode;
import com.baidu.autocomate.domain.message.repos.MessageRepository;
import com.baidu.autocomate.domain.sensitiveinfo.PromptSensitiveContentChecker;
import com.baidu.autocomate.domain.sensitiveinfo.PromptSensitiveInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

@Slf4j
@Service
public class AnalyzeServiceImpl implements AnalyzeService {
    @Autowired
    private AutoworkConversationService autoworkConversationService;
    @Autowired
    private ConversationRepository conversationRepository;
    @Autowired
    private MessageRepository messageRepository;
    @Autowired
    private PromptSensitiveContentChecker promptSensitiveContentChecker;

    @Override
    public AnalyzeResult analyze(@Nonnull AnalyzeCommand command) {
        command.validate();
        // 敏感信息检查，如果含有敏感则返回
        PromptSensitiveInfo sensitiveInfo =
                promptSensitiveContentChecker
                        .checkPromptSensitiveInfo(command.getUserName(), command.getQuery(), command.getIde());
        if (sensitiveInfo.isSensitive()) {
            throw new IllegalArgumentException(sensitiveInfo.getMessage());
        }
        AnalyzeStrategy analyzeStrategy = AnalyzeStrategyFactory.getAnalyzeStrategy(command.getSlash());
        // 1. 分析
        log.info("Analyze command: {}", GSON.toJson(command));
        AnalyzeResult result = analyzeStrategy.analyze(command);

        // 2. 创建会话
        ConversationEntity conversation;
        if (command.getConversationId() != null) {
            // 获取会话
            conversation = conversationRepository.getById(command.getConversationId());
            if (conversation == null) {
                throw new IllegalArgumentException(MessageHolder.get("analyze.error.conversationNotFound"));
            }
        } else {
            // 创建会话
            AutoworkConversationCreateCommand conversationCreateCommand = AutoworkConversationCreateCommand.builder()
                    .user(command.getUserName())
                    .build();
            conversation = autoworkConversationService.createConversation(conversationCreateCommand);
        }
        result.setConversationId(conversation.getId());

        // 3. 创建消息
        MessageEntity messageEntity;
        if (command.getMessageId() != null) {
            // 获取消息
            messageEntity = messageRepository.getMessageById(command.getMessageId());
            if (messageEntity == null) {
                throw new IllegalArgumentException(MessageHolder.get("analyze.error.messageNotFound"));
            }
            result.setMessageId(messageEntity.getId());
        } else {
            // 创建消息保存发送人发送的消息
            MessageContentEntity userMessageContent = new MessageContentEntity();
            userMessageContent.setType(MessageContentType.QUERY);
            userMessageContent.setDetail(command);
            userMessageContent.setInteractionMode(MessageInteractionMode.SSE);
            messageRepository.createByHuman(conversation.getId(), null, null, command.getUserName(),
                    userMessageContent, null, MessageContentSource.MONGO);

            // 创建响应消息
            MessageContentEntity responseMessageContent = new MessageContentEntity();
            responseMessageContent.setType(MessageContentType.ANSWER);
            responseMessageContent.setInteractionMode(MessageInteractionMode.SSE);
            MessageEntity responseMessage =
                    messageRepository.createByJob(conversation.getId(), null, null,
                    command.getSlash(),
                    command.getUserName(), responseMessageContent, null, MessageContentSource.MONGO);
            // 要返回响应的消息ID
            result.setMessageId(responseMessage.getId());
        }

        log.info("Analyze result: {}", GSON.toJson(result));
        return result;
    }
}
