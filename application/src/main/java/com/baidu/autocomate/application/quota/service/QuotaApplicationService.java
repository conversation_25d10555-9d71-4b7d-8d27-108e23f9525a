package com.baidu.autocomate.application.quota.service;

import com.baidu.autocomate.application.quota.cqe.UserQuotaQuery;
import com.baidu.autocomate.application.quota.cqe.QuotaConfigRequest;
import com.baidu.autocomate.domain.quota.entity.QuotaConfigEntity;
import com.baidu.autocomate.domain.quota.enums.ResourceType;

public interface QuotaApplicationService {
    Integer queryUserQuotaLeft(UserQuotaQuery query);

    /**
     * 查询用户剩余配额
     *
     * @param query  查询参数
     * @param config 配额配置
     * @return 剩余配额
     */
    Integer queryUserQuotaLeft(UserQuotaQuery query, QuotaConfigEntity config);

    Integer queryUserQuotaLimit(UserQuotaQuery query);

    /**
     * 判断是否超过 limit, 如果超了，直接返回false。
     * 如果没超过，则记录了用量记录，然后返回true
     *
     * @param userIdentifier
     * @param resourceType
     * @param limit
     * @return
     */
    boolean allowAndRecordUserQuota(String userIdentifier, ResourceType resourceType, Integer limit);

    /**
     * 记录用户使用配额
     *
     * @param userIdentifier 用户标识
     * @param resourceType   资源类型
     */
    void recordUserQuota(String userIdentifier, ResourceType resourceType);

    /**
     * 查询配额配置
     *
     * @param query 查询参数
     * @return 配额配置
     */
    QuotaConfigEntity queryQuotaConfig(UserQuotaQuery query);

    /**
     * 保存或更新配额配置
     * 如果配置不存在则创建，存在则更新
     *
     * @param request 配额配置请求
     */
    void saveOrUpdateQuotaConfig(QuotaConfigRequest request);
}