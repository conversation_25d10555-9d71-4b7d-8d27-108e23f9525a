package com.baidu.autocomate.application.conversation.service;

import com.baidu.autocomate.application.conversation.cqe.AutoworkConversationCreateCommand;
import com.baidu.autocomate.application.conversation.cqe.MessageCreateCommand;
import com.baidu.autocomate.domain.conversation.entity.ConversationEntity;
import com.baidu.autocomate.domain.message.entity.MessageEntity;

public interface AutoworkConversationService {

    /**
     * 创建Autowork会话
     * @param conversationCreateCommand
     */
    ConversationEntity createConversation(AutoworkConversationCreateCommand conversationCreateCommand);
    
    MessageEntity addMessage(ConversationEntity conversation, MessageCreateCommand messageCreateCommand);
}
