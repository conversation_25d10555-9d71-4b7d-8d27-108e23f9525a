package com.baidu.autocomate.application.metric.convertor;

import com.baidu.autocomate.application.metric.cqe.IdePasteMetricCommand;
import com.baidu.autocomate.domain.idepaste.entity.IdePasteMetricEntity;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.time.LocalDateTime;

@Mapper(componentModel = "spring")
public interface IdePasteMetricDTOConvertor {

    IdePasteMetricEntity convert(IdePasteMetricCommand command);

    @AfterMapping
    default void enrichReferenceTypes(@MappingTarget IdePasteMetricEntity entity) {
        entity.setCreateTime(LocalDateTime.now());
    }
}
