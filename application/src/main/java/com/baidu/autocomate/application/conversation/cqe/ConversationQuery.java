package com.baidu.autocomate.application.conversation.cqe;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Validated
public class ConversationQuery {
    
    @NotNull(message = "agent id can not be null")
    private Long agentId;
    
    @NotBlank(message = "user can not be null")
    private String user;
    
    private Integer limit = 10;
    
    private Integer offset = 0;
    
}
