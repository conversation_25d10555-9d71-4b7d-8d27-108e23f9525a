/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.service.impl;

import org.springframework.stereotype.Service;

import com.baidu.autocomate.application.adopt.cqe.AdoptCommand;
import com.baidu.autocomate.application.adopt.factory.AdoptStrategyFactory;
import com.baidu.autocomate.application.adopt.service.AdoptService;
import com.baidu.autocomate.application.adopt.strategy.AdoptStrategy;

@Service
public class AdoptServiceImpl implements AdoptService {

    @Override
    public void adopt(AdoptCommand adoptCommand) {
        adoptCommand.validate();
        AdoptStrategy adoptStrategy = AdoptStrategyFactory.getAdoptStrategy(adoptCommand.getSlash());
        // 只有部分需要走采纳策略，所以这里需要判断不为空才执行
        if (adoptStrategy != null) {
            adoptStrategy.adopt(adoptCommand);
        }
    }
}
