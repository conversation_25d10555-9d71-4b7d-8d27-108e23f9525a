/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.application.adopt.cqe;

import org.springframework.util.Assert;

import com.baidu.autocomate.application.common.advisor.Validation;
import com.baidu.autocomate.domain.i18n.MessageHolder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdoptCommand implements Validation {

    /**
     * 用户名或者License
     */
    private String userName;

    private String slash;

    /**
     * 埋点的uuid
     */
    private String adoptionUuid;

    /**
     * 采纳的内容
     */
    private String contentStr;

    @Override
    public void validate() {
        Assert.notNull(adoptionUuid, MessageHolder.get("param.is.null", "adoptionUuid"));
        Assert.notNull(slash, MessageHolder.get("param.is.null", "slash"));
    }
}
