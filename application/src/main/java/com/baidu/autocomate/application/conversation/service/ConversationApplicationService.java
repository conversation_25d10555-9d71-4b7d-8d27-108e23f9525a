package com.baidu.autocomate.application.conversation.service;

import com.baidu.autocomate.application.conversation.cqe.ConversationQuery;
import com.baidu.autocomate.application.conversation.cqe.MessageLikeCommand;
import com.baidu.autocomate.application.conversation.dto.ConversationDTO;
import com.baidu.autocomate.application.conversation.dto.ConversationMessageDTO;
import com.baidu.autocomate.domain.common.PageResult;
import com.baidu.autocomate.domain.conversation.entity.ConversationEntity;
import com.baidu.autocomate.domain.llm.entity.LLMMessageEntity;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;

import java.util.List;

public interface ConversationApplicationService {

    PageResult<ConversationDTO> queryConversations(ConversationQuery  conversationQuery);

     List<ConversationMessageDTO> conversationMessages(Long conversationId);

    ConversationEntity getConversationById(Long conversationId);

    void updateMessageLikeStatus(MessageLikeCommand updateMessageReactionStatus);

    List<LLMMessageEntity> conversationMessagesByWorkflow(Long workflowBuildId, MessageContentSource source);
}
