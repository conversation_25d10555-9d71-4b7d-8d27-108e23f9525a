package com.baidu.autocomate.application.conversation.service.imp;

import com.baidu.autocomate.application.conversation.convert.ConversationDTOConvertor;
import com.baidu.autocomate.application.conversation.cqe.ConversationQuery;
import com.baidu.autocomate.application.conversation.cqe.MessageLikeCommand;
import com.baidu.autocomate.application.conversation.dto.ConversationDTO;
import com.baidu.autocomate.application.conversation.dto.ConversationMessageDTO;
import com.baidu.autocomate.application.conversation.service.ConversationApplicationService;
import com.baidu.autocomate.domain.agent.entity.AgentInitEntity;
import com.baidu.autocomate.domain.agent.repos.AgentRepository;
import com.baidu.autocomate.domain.common.PageResult;
import com.baidu.autocomate.domain.conversation.entity.ConversationEntity;
import com.baidu.autocomate.domain.conversation.repos.ConversationRepository;
import com.baidu.autocomate.domain.jobs.aggregate.JobBuildEntity;
import com.baidu.autocomate.domain.jobs.repos.JobBuildRepository;
import com.baidu.autocomate.domain.llm.entity.LLMMessageEntity;
import com.baidu.autocomate.domain.llm.repos.LLMMessageRepository;
import com.baidu.autocomate.domain.message.beans.MessageSendBean;
import com.baidu.autocomate.domain.message.entity.MessageEntity;
import com.baidu.autocomate.domain.message.entity.MessageFeedbackEntity;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.message.enums.MessageType;
import com.baidu.autocomate.domain.message.repos.MessageRepository;
import com.baidu.autocomate.domain.workflow.build.WorkflowBuildEntity;
import com.baidu.autocomate.domain.workflow.repos.WorkflowBuildRepository;
import com.google.common.collect.ArrayListMultimap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConversationApplicationServiceImpl implements ConversationApplicationService {
    
    @Autowired
    private ConversationRepository conversationRepository;
    
    @Autowired
    private AgentRepository agentRepository;
    
    @Autowired
    private WorkflowBuildRepository workflowBuildRepository;
    
    @Autowired
    private JobBuildRepository jobBuildRepository;
    
    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private ConversationDTOConvertor conversationDTOConvertor;

    @Autowired
    private LLMMessageRepository llmMessageRepository;
    

    @Override
    public PageResult<ConversationDTO> queryConversations(ConversationQuery conversationQuery) {
        PageResult<ConversationEntity> result =
                conversationRepository.getConversationsByUsernameAndAgentId(conversationQuery.getUser(),
                        conversationQuery.getAgentId(), conversationQuery.getLimit(), conversationQuery.getOffset());
        List<ConversationEntity> conversationEntities = result.getRecords();
        if (CollectionUtils.isEmpty(conversationEntities)) {
            return PageResult.empty();
        }
        List<ConversationDTO> conversationDTOS = new ArrayList<>();
        conversationEntities.stream().filter(Objects::nonNull)
                .forEach(entity -> conversationDTOS.add(ConversationDTO.builder()
                        .conversationId(entity.getId())
                        .title(entity.getName())
                        .build()));
        // 组装init里面的内容
        assembleAgentInit(conversationDTOS);
        // 组装workflow build
        assembleWorkflowBuild(conversationDTOS);
        // 组装消息
        assembleMessage(conversationDTOS);
        // 排序,降序， todo: 这里有个问题，先查询最近的conversation，然后按照消息时间排序，是不对的，没严格按照消息时间降序，不太好修复
        conversationDTOS.sort(Comparator.comparing(ConversationDTO::getLastMessageTime).reversed());
        PageResult<ConversationDTO> dtoPageResult = new PageResult<>();
        dtoPageResult.setTotal(result.getTotal());
        dtoPageResult.setRecords(conversationDTOS);
        return dtoPageResult;
    }
    
    private void assembleAgentInit(List<ConversationDTO> conversationDTOS) {
        if (CollectionUtils.isEmpty(conversationDTOS)) {
            return;
        }
        List<Long> conversationIds = conversationDTOS.stream()
                .filter(Objects::nonNull)
                .map(ConversationDTO::getConversationId)
                .filter(Objects::nonNull).toList();
        List<AgentInitEntity> initEntities = agentRepository.getInitByConversationIds(conversationIds);
        if (CollectionUtils.isEmpty(initEntities)) {
            return;
        }
        Map<Long, AgentInitEntity> conversationIdInitMap = initEntities.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(AgentInitEntity::getConversationId, Function.identity(), (v1, v2) -> v1));
        for (ConversationDTO conversationDTO : conversationDTOS) {
            AgentInitEntity init = conversationIdInitMap.get(conversationDTO.getConversationId());
            if (null == init) {
                continue;
            }
            conversationDTO.setWorkspace(init.getWorkspace());
            conversationDTO.setKnowledge(init.getKnowledge());
            conversationDTO.setInitType(init.getType());
        }
    }
    
    private void assembleWorkflowBuild(List<ConversationDTO> conversationDTOS) {
        if (CollectionUtils.isEmpty(conversationDTOS)) {
            return;
        }
        List<Long> conversationIds = conversationDTOS.stream()
                .filter(Objects::nonNull)
                .map(ConversationDTO::getConversationId)
                .filter(Objects::nonNull).toList();
        List<WorkflowBuildEntity> workflowBuilds = 
                workflowBuildRepository.getConversationWorkflowBuilds(conversationIds);
        ArrayListMultimap<Long, WorkflowBuildEntity> conversationIdMap = ArrayListMultimap.create();
        workflowBuilds.stream().filter(Objects::nonNull)
                .forEach(workflowBuild -> conversationIdMap.put(workflowBuild.getConversationId(), workflowBuild));
        if (conversationIdMap.isEmpty()) {
            return;
        }
        for (ConversationDTO conversationDTO : conversationDTOS) {
            List<WorkflowBuildEntity> builds = conversationIdMap.get(conversationDTO.getConversationId());
            if (CollectionUtils.isEmpty(builds)) {
                continue;
            }
            // 最后一个workflow
            WorkflowBuildEntity workflowBuild = builds.get(builds.size() - 1);
            conversationDTO.setWorkflowBuildId(workflowBuild.getId());
            conversationDTO.setState(workflowBuild.getState());
        }
    }
    
    private void assembleMessage(List<ConversationDTO> conversationDTOS) {
        if (CollectionUtils.isEmpty(conversationDTOS)) {
            return;
        }
        List<Long> conversationIds = conversationDTOS.stream()
                .filter(Objects::nonNull)
                .map(ConversationDTO::getConversationId)
                .filter(Objects::nonNull).toList();
        List<MessageEntity> entities = messageRepository.conversationLatestMessageTime(conversationIds);
        Map<Long, MessageEntity> conversationIdMap = entities.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(MessageEntity::getConversationId, Function.identity(), (v1, v2) -> v2));
        for (ConversationDTO conversationDTO : conversationDTOS) {
            MessageEntity messageEntity = conversationIdMap.get(conversationDTO.getConversationId());
            if (null == messageEntity) {
                continue;
            }
            Instant instant = Instant.ofEpochMilli(messageEntity.getTimestamp());
            conversationDTO.setLastMessageTime(ZonedDateTime.ofInstant(instant, ZoneId.systemDefault()));
        }
    }

    @Override
    public List<ConversationMessageDTO> conversationMessages(Long conversationId) {
        // TODO: 是不是限制下个数？
        List<MessageEntity> entities = messageRepository.conversationMessage(conversationId);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        List<MessageSendBean> result = new ArrayList<>();
        for (MessageEntity entity : entities) {
            if (null == entity) {
                continue;
            }
            MessageSendBean messageSendBean = MessageSendBean.builder()
                    .messageId(entity.getId())
                    .content(entity.getContent())
                    .tasks(entity.getTasks())
                    .timestamp(entity.getTimestamp())
                    .workflowBuildId(entity.getWorkflowBuildId())
                    .conversationId(entity.getConversationId())
                    .messageType(entity.getSendType())
                    .likeStatus(entity.getLikeStatus())
                    .cancelled(entity.getCancelled())
                    .mode(entity.getMode())
                    .build();
            result.add(messageSendBean);
        }
        return conversationDTOConvertor.toConversationMessageDTOList(result);
    }

    @Override
    public ConversationEntity getConversationById(Long conversationId) {
        return conversationRepository.getById(conversationId);
    }

    @Override
    public void updateMessageLikeStatus(MessageLikeCommand command) {
        ConversationEntity conversation = getConversationById(command.getConversationId());
        if (conversation == null || !command.getUser().equals(conversation.getCreateBy())) {
            throw new IllegalArgumentException("conversation not exist");
        }
        MessageEntity messageEntity = messageRepository.getMessageById(command.getMessageId());
        if (messageEntity == null || !messageEntity.getConversationId().equals(command.getConversationId())) {
            throw new IllegalArgumentException("message not exist");
        }
        if (MessageType.USER.equals(messageEntity.getSendType())) {
            throw new IllegalArgumentException("can not like or unlike user send message");
        }
        if (command.getLikeStatus() != null && !command.getLikeStatus().equals(messageEntity.getLikeStatus())) {
            // 已经操作过了，不需要重复操作，避免点赞取消点赞时间被覆盖
            messageRepository.updateMessageLikeStatus(command.getMessageId(), command.getLikeStatus());
        }
        // 反馈为空，不记录
        if (StringUtils.isBlank(command.getFeedBack())) {
            return;
        }
        messageRepository.saveMessageFeedback(MessageFeedbackEntity.builder().feedback(command.getFeedBack())
                .messageId(command.getMessageId()).user(command.getUser()).feedTime(ZonedDateTime.now()).build());
    }

    @Override
    public List<LLMMessageEntity> conversationMessagesByWorkflow(Long workflowBuildId, MessageContentSource source) {
        List<JobBuildEntity> jobBuildEntities  = jobBuildRepository.loadJobBuildByWorkflowbuildId(workflowBuildId);
        JobBuildEntity jobBuild = jobBuildEntities.stream().filter(Objects::nonNull).findFirst().orElse(null);
        if (jobBuild == null) {
            return Collections.emptyList();
        }
        return llmMessageRepository.queryBySessionId(jobBuild.getLlmSessionId(), 
                        null == source ? MessageContentSource.MYSQL : source);
    }
}
