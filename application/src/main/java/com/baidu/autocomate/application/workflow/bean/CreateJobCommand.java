package com.baidu.autocomate.application.workflow.bean;

import com.baidu.autocomate.domain.jobs.aggregate.conf.JobType;
import lombok.Data;

@Data
public class CreateJobCommand {

    private String name;

    private Long workflowId;

    /**
     * 类型: Agent, ReAct
     */
    private JobType jobType;

    private boolean canModify;

    /**
     * 上游job conf
     */
    private Long upstreamId = -1L;

    private String prompt;

    private String tools;

    private String inputs;

    private String outputs;

    private String specifyConfig;
}
