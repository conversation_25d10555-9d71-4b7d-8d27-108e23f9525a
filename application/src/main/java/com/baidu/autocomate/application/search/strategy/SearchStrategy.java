package com.baidu.autocomate.application.search.strategy;

import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.application.search.factory.SearchStrategyFactory;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;

import javax.annotation.PostConstruct;

public interface SearchStrategy {
    @PostConstruct
    default void init() {
        SearchStrategyFactory.registerStrategy(getSlash(), this);
    }

    String getSlash();

    DataSearchResult search(SearchCommand searchCommand);
}
