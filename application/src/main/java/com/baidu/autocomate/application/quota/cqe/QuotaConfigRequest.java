package com.baidu.autocomate.application.quota.cqe;

import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuotaConfigRequest {
    @NotNull(message = "subjectType can not be null")
    private SubjectType subjectType = SubjectType.DEFAULT;

    private String subjectId;

    @NotNull(message = "resourceType can not be null")
    private ResourceType resourceType;

    @NotNull(message = "timeRange can not be null")
    private TimeRange timeRange = TimeRange.TOTAL;

    @NotNull(message = "loginTimes can not be null")
    private Integer loginTimes;
} 