package com.baidu.autocomate.application.analyze.cqe;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.Before;
import org.junit.Test;

import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;

public class AnalyzeCommandTest {

    private AnalyzeCommand analyzeCommand;

    @Before
    public void setUp() {
        analyzeCommand = new AnalyzeCommand();
    }

    @Test
    public void testAllFields() {
        analyzeCommand.setConversationId(1L);
        analyzeCommand.setMessageId(2L);
        analyzeCommand.setRepoId("repo123");
        analyzeCommand.setIntent(true);
        analyzeCommand.setSlash("/command");
        analyzeCommand.setQuery("user query");
        analyzeCommand.setNeedQueryRewrite(false);
        analyzeCommand.setSelection("selected code");
        analyzeCommand.setUserName("testUser");

        List<AutoworkContext> contexts = Arrays.asList(new AutoworkContext(), new AutoworkContext());
        analyzeCommand.setContexts(contexts);

        analyzeCommand.setIde("IDE_TYPE");
        analyzeCommand.setPluginVersion("1.0.0");
        analyzeCommand.setCurrentFilePath("/path/to/file");
        analyzeCommand.setCursorLine(10);

        assertEquals(Long.valueOf(1L), analyzeCommand.getConversationId());
        assertEquals(Long.valueOf(2L), analyzeCommand.getMessageId());
        assertEquals("repo123", analyzeCommand.getRepoId());
        assertTrue(analyzeCommand.isIntent());
        assertEquals("/command", analyzeCommand.getSlash());
        assertEquals("user query", analyzeCommand.getQuery());
        assertFalse(analyzeCommand.isNeedQueryRewrite());
        assertEquals("selected code", analyzeCommand.getSelection());
        assertEquals("testUser", analyzeCommand.getUserName());
        assertEquals(contexts, analyzeCommand.getContexts());
        assertEquals("IDE_TYPE", analyzeCommand.getIde());
        assertEquals("1.0.0", analyzeCommand.getPluginVersion());
        assertEquals("/path/to/file", analyzeCommand.getCurrentFilePath());
        assertEquals(Integer.valueOf(10), analyzeCommand.getCursorLine());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testValidateUserNameNull() {
        analyzeCommand.setUserName(null);
        analyzeCommand.setSlash("/command");
        analyzeCommand.validate();
    }

    @Test(expected = IllegalArgumentException.class)
    public void testValidateSlashNull() {
        analyzeCommand.setUserName("testUser");
        analyzeCommand.setSlash(null);
        analyzeCommand.validate();
    }

    @Test
    public void testValidateSuccess() {
        analyzeCommand.setUserName("testUser");
        analyzeCommand.setSlash("/command");
        analyzeCommand.validate();
        // No exception should be thrown
    }
}