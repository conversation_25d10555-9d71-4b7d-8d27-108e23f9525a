package com.baidu.autocomate.application.analyze.strategy.composer;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.quota.cqe.UserQuotaQuery;
import com.baidu.autocomate.application.quota.exception.ComposerQuotaLimitException;
import com.baidu.autocomate.application.quota.exception.ComposerUnauthorizedException;
import com.baidu.autocomate.application.quota.service.QuotaApplicationService;
import com.baidu.autocomate.domain.llm.model.claude.ClaudeSKManager;
import com.baidu.autocomate.domain.quota.entity.QuotaConfigEntity;
import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import com.baidu.autocomate.domain.utils.EnvironmentUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.client.RestTemplate;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ComposerQuotaCheckerTest {

    @Mock
    private ClaudeSKManager claudeSKManager;

    @Mock
    private QuotaApplicationService quotaApplicationService;

    @Mock
    private EnvironmentUtils environmentUtils;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private ComposerQuotaChecker composerQuotaChecker;

    private AnalyzeCommand analyzeCommand;

    @Before
    public void setUp() {
        analyzeCommand = AnalyzeCommand.builder()
                .userName("testUser")
                .build();
    }

    private static class UserQuotaQueryMatcher implements ArgumentMatcher<UserQuotaQuery> {
        private final SubjectType expectedSubjectType;
        private final ResourceType expectedResourceType;
        private final TimeRange expectedTimeRange;

        public UserQuotaQueryMatcher(SubjectType expectedSubjectType, ResourceType expectedResourceType, TimeRange expectedTimeRange) {
            this.expectedSubjectType = expectedSubjectType;
            this.expectedResourceType = expectedResourceType;
            this.expectedTimeRange = expectedTimeRange;
        }

        @Override
        public boolean matches(UserQuotaQuery query) {
            return query != null 
                && expectedSubjectType.equals(query.getSubjectType())
                && expectedResourceType.equals(query.getResourceType())
                && expectedTimeRange.equals(query.getTimeRange());
        }
    }

    @Test
    public void testInternalEnvSuccess() {
        // 设置为内部环境
        when(environmentUtils.isInternal()).thenReturn(true);

        // 设置用户配额
        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(10)
                .timeRange(TimeRange.DAY)
                .build();

        // 验证查询参数
        ArgumentCaptor<UserQuotaQuery> queryCaptor = ArgumentCaptor.forClass(UserQuotaQuery.class);
        when(quotaApplicationService.queryQuotaConfig(queryCaptor.capture())).thenReturn(userConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(UserQuotaQuery.class), any(QuotaConfigEntity.class))).thenReturn(5);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);

        // 验证调用
        verify(quotaApplicationService).recordUserQuota("testUser", ResourceType.COMPOSER);
        
        // 验证查询参数正确性
        UserQuotaQuery capturedQuery = queryCaptor.getValue();
        assertEquals(SubjectType.DEFAULT, capturedQuery.getSubjectType());
        assertEquals(ResourceType.COMPOSER, capturedQuery.getResourceType());
        assertEquals(TimeRange.DAY, capturedQuery.getTimeRange());
    }

    @Test(expected = ComposerQuotaLimitException.class)
    public void testInternalEnvQuotaExceeded() {
        when(environmentUtils.isInternal()).thenReturn(true);

        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(10)
                .timeRange(TimeRange.DAY)
                .build();
        when(quotaApplicationService.queryQuotaConfig(any(UserQuotaQuery.class))).thenReturn(userConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(UserQuotaQuery.class), any(QuotaConfigEntity.class))).thenReturn(0);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService, never()).recordUserQuota(any(), any());
    }

    @Test
    public void testInternalEnvWithDefaultQuota() {
        when(environmentUtils.isInternal()).thenReturn(true);

        // 用户配额为空，使用默认配额
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.DAY))))
                .thenReturn(null);

        QuotaConfigEntity defaultConfig = QuotaConfigEntity.builder()
                .loginTimes(5)
                .timeRange(TimeRange.DAY)
                .build();
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.DEFAULT, ResourceType.COMPOSER, TimeRange.DAY))))
                .thenReturn(defaultConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(), eq(defaultConfig))).thenReturn(3);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService).recordUserQuota("testUser", ResourceType.COMPOSER);
    }

    @Test
    public void testSaasEnvSuccess() {
        when(environmentUtils.isInternal()).thenReturn(false);

        // Mock orgId 获取
        ComposerQuotaChecker.OrgResponse orgResponse = new ComposerQuotaChecker.OrgResponse();
        orgResponse.setStatus("OK");
        ComposerQuotaChecker.OrgData orgData = new ComposerQuotaChecker.OrgData();
        orgData.setOrgId("testOrg");
        orgResponse.setData(orgData);
        when(restTemplate.getForObject(any(String.class), eq(ComposerQuotaChecker.OrgResponse.class)))
                .thenReturn(orgResponse);

        // 设置用户配额
        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(100)
                .timeRange(TimeRange.TOTAL)
                .build();

        // 设置组织配额
        QuotaConfigEntity orgConfig = QuotaConfigEntity.builder()
                .loginTimes(1000)
                .timeRange(TimeRange.TOTAL)
                .build();

        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(userConfig);
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.ORGANIZATION, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(orgConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(UserQuotaQuery.class), any(QuotaConfigEntity.class))).thenReturn(50);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);

        verify(quotaApplicationService).recordUserQuota("testUser", ResourceType.COMPOSER);
        verify(quotaApplicationService, times(3)).queryQuotaConfig(any());
        verify(quotaApplicationService, atLeastOnce()).queryUserQuotaLeft(any(), any());
    }

    @Test
    public void testSaasEnvWithDefaultQuotaOnly() {
        when(environmentUtils.isInternal()).thenReturn(false);

        // 用户和组织配额都为空，只有默认配额
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(null);

        QuotaConfigEntity defaultConfig = QuotaConfigEntity.builder()
                .loginTimes(50)
                .timeRange(TimeRange.TOTAL)
                .build();
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.DEFAULT, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(defaultConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(), eq(defaultConfig))).thenReturn(10);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService).recordUserQuota("testUser", ResourceType.COMPOSER);
    }

    @Test(expected = ComposerQuotaLimitException.class)
    public void testSaasEnvUserQuotaExceeded() {
        when(environmentUtils.isInternal()).thenReturn(false);

        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(100)
                .timeRange(TimeRange.TOTAL)
                .build();

        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(userConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(UserQuotaQuery.class), any(QuotaConfigEntity.class))).thenReturn(0);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService, never()).recordUserQuota(any(), any());
    }

    @Test(expected = ComposerQuotaLimitException.class)
    public void testSaasEnvOrgQuotaExceeded() {
        when(environmentUtils.isInternal()).thenReturn(false);

        // Mock orgId 获取
        ComposerQuotaChecker.OrgResponse orgResponse = new ComposerQuotaChecker.OrgResponse();
        orgResponse.setStatus("OK");
        ComposerQuotaChecker.OrgData orgData = new ComposerQuotaChecker.OrgData();
        orgData.setOrgId("testOrg");
        orgResponse.setData(orgData);
        when(restTemplate.getForObject(any(String.class), eq(ComposerQuotaChecker.OrgResponse.class)))
                .thenReturn(orgResponse);

        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(100)
                .timeRange(TimeRange.TOTAL)
                .build();

        QuotaConfigEntity orgConfig = QuotaConfigEntity.builder()
                .loginTimes(1000)
                .timeRange(TimeRange.TOTAL)
                .build();

        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(userConfig);
        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.ORGANIZATION, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(orgConfig);

        // 用户配额充足但组织配额不足
        when(quotaApplicationService.queryUserQuotaLeft(any(), eq(userConfig))).thenReturn(50);
        when(quotaApplicationService.queryUserQuotaLeft(any(), eq(orgConfig))).thenReturn(0);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService, never()).recordUserQuota(any(), any());
    }

    @Test(expected = ComposerUnauthorizedException.class)
    public void testSaasEnvNoQuotaConfig() {
        when(environmentUtils.isInternal()).thenReturn(false);
        when(quotaApplicationService.queryQuotaConfig(any(UserQuotaQuery.class))).thenReturn(null);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService, never()).recordUserQuota(any(), any());
    }

    @Test
    public void testSaasEnvWithoutOrgId() {
        when(environmentUtils.isInternal()).thenReturn(false);
        
        // 创建没有组织ID的命令
        analyzeCommand = AnalyzeCommand.builder()
                .userName("testUser")
                .build();

        QuotaConfigEntity userConfig = QuotaConfigEntity.builder()
                .loginTimes(100)
                .timeRange(TimeRange.TOTAL)
                .build();

        when(quotaApplicationService.queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.USER, ResourceType.COMPOSER, TimeRange.TOTAL))))
                .thenReturn(userConfig);
        when(quotaApplicationService.queryUserQuotaLeft(any(), eq(userConfig))).thenReturn(50);

        composerQuotaChecker.checkAndRecordUsage(analyzeCommand);
        verify(quotaApplicationService).recordUserQuota("testUser", ResourceType.COMPOSER);
        
        // 验证没有查询组织配额
        verify(quotaApplicationService, never()).queryQuotaConfig(argThat(new UserQuotaQueryMatcher(SubjectType.ORGANIZATION, ResourceType.COMPOSER, TimeRange.TOTAL)));
    }
}