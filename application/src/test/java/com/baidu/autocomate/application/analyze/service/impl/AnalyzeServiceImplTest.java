package com.baidu.autocomate.application.analyze.service.impl;

import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import com.baidu.autocomate.domain.sensitiveinfo.PromptSensitiveInfo;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;
import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.service.impl.AnalyzeServiceImpl;
import com.baidu.autocomate.domain.sensitiveinfo.PromptSensitiveContentChecker;
import org.junit.Test;

@RunWith(MockitoJUnitRunner.class)
public class AnalyzeServiceImplTest{

    @Mock
    private PromptSensitiveContentChecker promptSensitiveContentChecker;

    @InjectMocks
    private AnalyzeServiceImpl analyzeService;

    private AnalyzeCommand command;

    @Before
    public void setUp() {
        command = mock(AnalyzeCommand.class);
        when(command.getUserName()).thenReturn("testUser");
        when(command.getQuery()).thenReturn("testQuery");
        when(command.getIde()).thenReturn("testIde");
    }

    // testAnalyze_SensitiveContent 用于测试 analyze
    // generated by Comate
    @Test(expected = IllegalArgumentException.class)
    public void testAnalyze_SensitiveContent() {
        PromptSensitiveInfo sensitiveInfo = mock(PromptSensitiveInfo.class);
        when(sensitiveInfo.isSensitive()).thenReturn(true);
        when(sensitiveInfo.getMessage()).thenReturn("Sensitive content detected");
        when(promptSensitiveContentChecker.checkPromptSensitiveInfo(anyString(), anyString(), anyString())).thenReturn(sensitiveInfo);
    
        analyzeService.analyze(command);
    }

}