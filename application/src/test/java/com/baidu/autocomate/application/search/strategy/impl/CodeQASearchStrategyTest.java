package com.baidu.autocomate.application.search.strategy.impl;

import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import com.baidu.autocomate.domain.workflow.enums.Slash;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

@RunWith(MockitoJUnitRunner.class)
public class CodeQASearchStrategyTest{

    @InjectMocks
    private CodeQASearchStrategy codeQASearchStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // testGetSlash 用于测试 getSlash
    // generated by Comate
    @Test
    public void testGetSlash() {
        // Act
        String result = codeQASearchStrategy.getSlash();
    
        // Assert
        assertEquals(Slash.CODE_QA.getName(), result);
    }

}