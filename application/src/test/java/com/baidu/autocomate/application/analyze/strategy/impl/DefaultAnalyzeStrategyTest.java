package com.baidu.autocomate.application.analyze.strategy.impl;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.baidu.autocomate.domain.analyze.enums.AutoworkContextType;
import com.google.common.collect.Lists;

import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.knowledge.code.bean.KnowledgeSearchRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.jobs.executor.code.QueryUtil;
import com.baidu.autocomate.domain.llm.repos.LLMSessionRepository;
import com.baidu.autocomate.domain.searcher.searcher.code.RepoMetaCodeRetrieval;
import com.baidu.autocomate.domain.think.bean.SearchRule;
import com.baidu.autocomate.domain.think.bean.SearchStrategy;
import com.baidu.autocomate.domain.think.bean.ThinkIntent;
import com.baidu.autocomate.domain.think.handler.IntentHandler;

@RunWith(MockitoJUnitRunner.class)
public class DefaultAnalyzeStrategyTest {

    @InjectMocks
    private DefaultAnalyzeStrategy defaultAnalyzeStrategy;

    @Mock
    private QueryUtil queryUtil;

    @Mock
    private IntentHandler intentHandler;

    @Mock
    private LLMSessionRepository llmSessionRepository;

    @Mock
    private RepoMetaCodeRetrieval repoMetaCodeRetrieval;

    ThinkIntent intent = new ThinkIntent();
    AnalyzeCommand analyzeCommand = new AnalyzeCommand();
    AutoworkContext context = new AutoworkContext();

    @Before
    public void setUp() {
        context.setRetrievalType(KnowledgeSearchRequest.KnowledgeRetrievalType.TEXT);
        context.setType(AutoworkContextType.NORMAL);
        context.setId("xxxxx");
        MockitoAnnotations.initMocks(this);
        analyzeCommand.setIntent(true);
        analyzeCommand.setConversationId(1L);
        analyzeCommand.setMessageId(1L);
        analyzeCommand.setRepoId("repo");
        analyzeCommand.setQuery("query");
        analyzeCommand.setSelection("");
        analyzeCommand.setCurrentFilePath("/path/to/file");
        analyzeCommand.setCursorLine(5);
        analyzeCommand.setUserName("user");
        analyzeCommand.setPluginVersion("plugin_version");
        analyzeCommand.setIde("ide");
        analyzeCommand.setContexts(Lists.newArrayList(context));
        analyzeCommand.setNeedGraph(true);
    }

    @Test
    public void testProcessGraphSearchStrategy_withGraphNeeded() {
        List<SearchStrategy> searchStrategies = new ArrayList<>();
        SearchStrategy graphStrategy = SearchStrategy.builder()
                .rule(SearchRule.FILE_GRAPH)
                .build();
        searchStrategies.add(graphStrategy);
        intent.setSearchStrategies(searchStrategies);

        when(intentHandler.intent(anyString(), any(), nullable(Long.class), any(), any(), anyList(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyBoolean()))
                .thenReturn(intent);

        // Act
        AnalyzeResult analyzeResult = defaultAnalyzeStrategy.analyze(analyzeCommand);

        // Assert
        List<SearchStrategy> resultStrategies = analyzeResult.getIntentStrategies();
        assertEquals(1, resultStrategies.size());
        assertEquals(SearchRule.FILE_GRAPH, resultStrategies.get(0).getRule());
    }

    @Test
    public void testProcessGraphSearchStrategy_withoutGraphNeeded() {
        List<SearchStrategy> searchStrategies = new ArrayList<>();
        SearchStrategy graphStrategy = SearchStrategy.builder()
                .rule(SearchRule.FILE_GRAPH)
                .build();
        searchStrategies.add(graphStrategy);
        intent.setSearchStrategies(searchStrategies);

        when(intentHandler.intent(anyString(), any(), nullable(Long.class), any(), any(), anyList(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyBoolean()))
                .thenReturn(intent);

        // Act
        AnalyzeResult analyzeResult = defaultAnalyzeStrategy.analyze(analyzeCommand);

        // Assert
        List<SearchStrategy> resultStrategies = analyzeResult.getIntentStrategies();
        assertEquals(1, resultStrategies.size());
    }

    @Test
    public void testProcessGraphSearchStrategy_withEmptySearchStrategies() {
        List<SearchStrategy> searchStrategies = Collections.emptyList();
        intent.setSearchStrategies(searchStrategies);

        when(intentHandler.intent(anyString(), any(), nullable(Long.class), any(), any(), anyList(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyBoolean()))
                .thenReturn(intent);

        // Act
        AnalyzeResult analyzeResult = defaultAnalyzeStrategy.analyze(analyzeCommand);

        // Assert
        List<SearchStrategy> resultStrategies = analyzeResult.getIntentStrategies();
        assertEquals(0, resultStrategies.size());
    }
}