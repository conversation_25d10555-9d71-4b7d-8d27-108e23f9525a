package com.baidu.autocomate.application.quota.service.impl;

import com.baidu.autocomate.application.account.service.AccountApplicationService;
import com.baidu.autocomate.application.permission.PermissionService;
import com.baidu.autocomate.application.quota.cqe.QuotaConfigRequest;
import com.baidu.autocomate.application.quota.cqe.UserQuotaQuery;
import com.baidu.autocomate.domain.account.enums.UserAccountType;
import com.baidu.autocomate.domain.quota.entity.QuotaConfigEntity;
import com.baidu.autocomate.domain.quota.entity.QuotaRecordEntity;
import com.baidu.autocomate.domain.quota.enums.ResourceType;
import com.baidu.autocomate.domain.quota.enums.SubjectType;
import com.baidu.autocomate.domain.quota.enums.TimeRange;
import com.baidu.autocomate.domain.quota.repos.QuotaRepository;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QuotaApplicationServiceImplTest {

    @InjectMocks
    private QuotaApplicationServiceImpl quotaApplicationService;

    @Mock
    private PermissionService permissionService;

    @Mock
    private AccountApplicationService accountApplicationService;

    @Mock
    private QuotaRepository quotaRepository;

    @Test
    public void testQueryUserQuotaLimitSystemManager() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMATE_WEB)
                .subjectId("systemManager")
                .build();
    
        when(permissionService.isSystemManager("systemManager")).thenReturn(true);
    
        // Act
        Integer result = quotaApplicationService.queryUserQuotaLimit(query);
    
        // Assert
        assertEquals(Integer.valueOf(Integer.MAX_VALUE), result);
        verify(permissionService).isSystemManager("systemManager");
        verifyNoInteractions(accountApplicationService);
        verifyNoInteractions(quotaRepository);
    }

    @Test
    public void testQueryUserQuotaLimitComateWebNoConfig() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMATE_WEB)
                .subjectId("user")
                .build();
    
        when(permissionService.isSystemManager("user")).thenReturn(false);
        when(quotaRepository.queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL))
                .thenReturn(null);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLimit(query);
    
        // Assert
        assertEquals(NumberUtils.INTEGER_ZERO, result);
        verify(permissionService).isSystemManager("user");
        verify(quotaRepository).queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL);
        verifyNoInteractions(accountApplicationService);
        verifyNoMoreInteractions(permissionService, quotaRepository);
    }

    @Test
    public void testQueryUserQuotaLimitComateWebWithConfig() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMATE_WEB)
                .subjectId("user")
                .build();

        QuotaConfigEntity config = mock(QuotaConfigEntity.class);
        when(config.getQuotaLimit(UserAccountType.OTHER)).thenReturn(100);
    
        when(permissionService.isSystemManager("user")).thenReturn(false);
        when(quotaRepository.queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL))
                .thenReturn(config);
        when(accountApplicationService.getUserAccountType("user")).thenReturn(UserAccountType.OTHER);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLimit(query);
    
        // Assert
        assertEquals(Integer.valueOf(100), result);
        verify(permissionService).isSystemManager("user");
        verify(quotaRepository).queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL);
        verify(accountApplicationService).getUserAccountType("user");
        verify(config).getQuotaLimit(UserAccountType.OTHER);
        verifyNoMoreInteractions(permissionService, quotaRepository, accountApplicationService, config);
    }

    @Test
    public void testQueryUserQuotaLimitComateWebWithConfigLicence() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMATE_WEB)
                .subjectId("licenceUser")
                .build();

        QuotaConfigEntity config = mock(QuotaConfigEntity.class);
        when(config.getQuotaLimit(UserAccountType.LICENCE)).thenReturn(500);
    
        when(permissionService.isSystemManager("licenceUser")).thenReturn(false);
        when(quotaRepository.queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL))
                .thenReturn(config);
        when(accountApplicationService.getUserAccountType("licenceUser")).thenReturn(UserAccountType.LICENCE);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLimit(query);
    
        // Assert
        assertEquals(Integer.valueOf(500), result);
        verify(permissionService).isSystemManager("licenceUser");
        verify(quotaRepository).queryQuotaConfig(SubjectType.DEFAULT, null, ResourceType.COMATE_WEB, TimeRange.TOTAL);
        verify(accountApplicationService).getUserAccountType("licenceUser");
        verify(config).getQuotaLimit(UserAccountType.LICENCE);
        verifyNoMoreInteractions(permissionService, quotaRepository, accountApplicationService, config);
    }

    @Test
    public void testQueryUserQuotaLimitNonComateWeb() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMPOSER)
                .subjectId("user")
                .subjectType(SubjectType.USER)
                .timeRange(TimeRange.DAY)
                .build();

        QuotaConfigEntity config = QuotaConfigEntity.builder()
                .loginTimes(50)
                .timeRange(TimeRange.DAY)
                .build();
    
        when(quotaRepository.queryQuotaConfig(SubjectType.USER, "user", ResourceType.COMPOSER, TimeRange.DAY))
                .thenReturn(config);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLimit(query);
    
        // Assert
        assertEquals(Integer.valueOf(50), result);
        verify(quotaRepository).queryQuotaConfig(SubjectType.USER, "user", ResourceType.COMPOSER, TimeRange.DAY);
        verifyNoInteractions(permissionService, accountApplicationService);
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testQueryUserQuotaLeftWithNullConfig() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMPOSER)
                .subjectId("user")
                .subjectType(SubjectType.USER)
                .timeRange(TimeRange.DAY)
                .build();

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLeft(query, null);

        // Assert
        assertEquals(NumberUtils.INTEGER_ZERO, result);
        verifyNoInteractions(quotaRepository);
    }

    @Test
    public void testQueryUserQuotaLeftWithConfig() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMPOSER)
                .subjectId("user")
                .subjectType(SubjectType.USER)
                .timeRange(TimeRange.DAY)
                .build();

        QuotaConfigEntity config = QuotaConfigEntity.builder()
                .loginTimes(100)
                .timeRange(TimeRange.DAY)
                .build();

        when(quotaRepository.queryUserQuotaLeft("user", ResourceType.COMPOSER, 100, TimeRange.DAY))
                .thenReturn(50);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLeft(query, config);

        // Assert
        assertEquals(Integer.valueOf(50), result);
        verify(quotaRepository).queryUserQuotaLeft("user", ResourceType.COMPOSER, 100, TimeRange.DAY);
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testQueryUserQuotaLeftWithoutConfig() {
        // Arrange
        UserQuotaQuery query = UserQuotaQuery.builder()
                .resourceType(ResourceType.COMPOSER)
                .subjectId("user")
                .subjectType(SubjectType.USER)
                .timeRange(TimeRange.DAY)
                .build();

        when(quotaRepository.queryQuotaConfig(SubjectType.USER, "user", ResourceType.COMPOSER, TimeRange.DAY))
                .thenReturn(null);

        // Act
        Integer result = quotaApplicationService.queryUserQuotaLeft(query);

        // Assert
        assertEquals(NumberUtils.INTEGER_ZERO, result);
        verify(quotaRepository).queryQuotaConfig(SubjectType.USER, "user", ResourceType.COMPOSER, TimeRange.DAY);
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testAllowAndRecordUserQuotaSuccess() {
        // Arrange
        String userIdentifier = "user";
        ResourceType resourceType = ResourceType.COMPOSER;
        Integer limit = 100;

        when(quotaRepository.allowAndRecordUserQuota(userIdentifier, resourceType, limit, TimeRange.TOTAL))
                .thenReturn(true);

        // Act
        boolean result = quotaApplicationService.allowAndRecordUserQuota(userIdentifier, resourceType, limit);

        // Assert
        assertTrue(result);
        verify(quotaRepository).allowAndRecordUserQuota(userIdentifier, resourceType, limit, TimeRange.TOTAL);
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testAllowAndRecordUserQuotaFail() {
        // Arrange
        String userIdentifier = "user";
        ResourceType resourceType = ResourceType.COMPOSER;
        Integer limit = 100;

        when(quotaRepository.allowAndRecordUserQuota(userIdentifier, resourceType, limit, TimeRange.TOTAL))
                .thenReturn(false);

        // Act
        boolean result = quotaApplicationService.allowAndRecordUserQuota(userIdentifier, resourceType, limit);

        // Assert
        assertFalse(result);
        verify(quotaRepository).allowAndRecordUserQuota(userIdentifier, resourceType, limit, TimeRange.TOTAL);
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testRecordUserQuota() {
        // Arrange
        String userIdentifier = "user";
        ResourceType resourceType = ResourceType.COMPOSER;

        ArgumentCaptor<QuotaRecordEntity> recordCaptor = ArgumentCaptor.forClass(QuotaRecordEntity.class);

        // Act
        quotaApplicationService.recordUserQuota(userIdentifier, resourceType);

        // Assert
        verify(quotaRepository).recordUserQuota(recordCaptor.capture());
        
        QuotaRecordEntity capturedRecord = recordCaptor.getValue();
        assertEquals(userIdentifier, capturedRecord.getUserIdentifier());
        assertEquals(resourceType, capturedRecord.getResourceType());
        // 由于 LocalDateTime.now() 的动态性，我们只验证 createAt 不为空
        assertTrue(capturedRecord.getCreateAt() != null);
        
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test
    public void testSaveOrUpdateQuotaConfigSuccess() {
        // Arrange
        QuotaConfigRequest request = new QuotaConfigRequest();
        request.setSubjectType(SubjectType.USER);
        request.setSubjectId("testUser");
        request.setResourceType(ResourceType.COMPOSER);
        request.setTimeRange(TimeRange.TOTAL);
        request.setLoginTimes(100);

        ArgumentCaptor<QuotaConfigEntity> configCaptor = ArgumentCaptor.forClass(QuotaConfigEntity.class);

        // Act
        quotaApplicationService.saveOrUpdateQuotaConfig(request);

        // Assert
        verify(quotaRepository).saveOrUpdateQuotaConfig(configCaptor.capture());
        
        QuotaConfigEntity capturedConfig = configCaptor.getValue();
        assertEquals(SubjectType.USER, capturedConfig.getSubjectType());
        assertEquals("testUser", capturedConfig.getSubjectId());
        assertEquals(ResourceType.COMPOSER, capturedConfig.getResourceType());
        assertEquals(TimeRange.TOTAL, capturedConfig.getTimeRange());
        assertEquals(Integer.valueOf(100), capturedConfig.getLoginTimes());
        
        verifyNoMoreInteractions(quotaRepository);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSaveOrUpdateQuotaConfigFailure() {
        // Arrange
        QuotaConfigRequest request = new QuotaConfigRequest();
        request.setSubjectType(SubjectType.USER);
        request.setSubjectId("testUser");
        request.setResourceType(ResourceType.COMPOSER);
        request.setTimeRange(TimeRange.TOTAL);
        request.setLoginTimes(100);

        // Mock repository to throw exception
        doThrow(new RuntimeException("DB error")).when(quotaRepository).saveOrUpdateQuotaConfig(any());

        // Act
        quotaApplicationService.saveOrUpdateQuotaConfig(request);
    }
}