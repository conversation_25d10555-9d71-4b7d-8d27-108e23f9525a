package com.baidu.autocomate.application.analyze.strategy.composer;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.quota.exception.ComposerUnauthorizedException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ComposerWhiteListCheckerTest {

    @InjectMocks
    private ComposerWhiteListChecker composerWhiteListChecker;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private SetOperations<String, String> setOperations;

    private static final String WHITELIST_KEY = "composer:whitelist";
    private static final String TEST_USER = "testUser";

    @Before
    public void setup() {
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
    }

    @Test
    public void testCheckWhitelistWhenDisabled() {
        // 设置白名单检查为禁用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", false);

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(TEST_USER);

        // 执行检查
        composerWhiteListChecker.checkWhitelist(command);

        // 验证redis操作没有被调用
        verify(redisTemplate, never()).opsForSet();
    }

    @Test
    public void testCheckWhitelistWhenUserInWhitelist() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);
        
        // 模拟用户在白名单中
        when(setOperations.isMember(eq(WHITELIST_KEY), eq(TEST_USER))).thenReturn(true);

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(TEST_USER);

        // 执行检查
        composerWhiteListChecker.checkWhitelist(command);

        // 验证redis操作被调用
        verify(setOperations).isMember(WHITELIST_KEY, TEST_USER);
    }

    @Test(expected = ComposerUnauthorizedException.class)
    public void testCheckWhitelistWhenUserNotInWhitelist() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);
        
        // 模拟用户不在白名单中
        when(setOperations.isMember(eq(WHITELIST_KEY), eq(TEST_USER))).thenReturn(false);

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(TEST_USER);

        // 执行检查，应该抛出异常
        composerWhiteListChecker.checkWhitelist(command);
    }

    @Test(expected = ComposerUnauthorizedException.class)
    public void testCheckWhitelistWhenRedisReturnsNull() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);
        
        // 模拟redis返回null
        when(setOperations.isMember(eq(WHITELIST_KEY), eq(TEST_USER))).thenReturn(null);

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(TEST_USER);

        // 执行检查，应该抛出异常
        composerWhiteListChecker.checkWhitelist(command);
    }

    @Test(expected = ComposerUnauthorizedException.class)
    public void testCheckWhitelistWhenUserNameIsNull() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(null);

        // 执行检查，应该抛出异常
        composerWhiteListChecker.checkWhitelist(command);
    }

    @Test(expected = RuntimeException.class)
    public void testCheckWhitelistWhenRedisThrowsException() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);
        
        // 模拟redis抛出异常
        when(setOperations.isMember(eq(WHITELIST_KEY), eq(TEST_USER)))
                .thenThrow(new RuntimeException("Redis error"));

        AnalyzeCommand command = new AnalyzeCommand();
        command.setUserName(TEST_USER);

        // 执行检查，应该抛出异常
        composerWhiteListChecker.checkWhitelist(command);
    }

    @Test
    public void testCheckWhitelistWithDifferentUserNames() {
        // 设置白名单检查为启用
        ReflectionTestUtils.setField(composerWhiteListChecker, "enableWhiteList", true);
        
        // 模拟不同用户名的情况
        when(setOperations.isMember(eq(WHITELIST_KEY), eq("user1"))).thenReturn(true);
        when(setOperations.isMember(eq(WHITELIST_KEY), eq("user2"))).thenReturn(false);

        // 测试在白名单中的用户
        AnalyzeCommand command1 = new AnalyzeCommand();
        command1.setUserName("user1");
        composerWhiteListChecker.checkWhitelist(command1);

        // 测试不在白名单中的用户
        AnalyzeCommand command2 = new AnalyzeCommand();
        command2.setUserName("user2");
        try {
            composerWhiteListChecker.checkWhitelist(command2);
            fail("Should throw ComposerUnauthorizedException");
        } catch (ComposerUnauthorizedException e) {
            assertEquals("用户不在白名单", e.getMessage());
        }
    }
}