package com.baidu.autocomate.application.question.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.baidu.autocomate.application.question.bean.AskRelatedQuestionRequest;
import com.baidu.autocomate.application.question.bean.AskRelatedQuestionResponse;
import com.baidu.autocomate.domain.analyze.bean.AutoworkContext;
import com.baidu.autocomate.domain.llm.bean.CompletionContent;
import com.baidu.autocomate.domain.llm.bean.CompletionResult;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieRequest;

@RunWith(MockitoJUnitRunner.class)
public class RelatedQuestionServiceImplTest {

    @InjectMocks
    private RelatedQuestionServiceImpl relatedQuestionService;

    @Mock
    private CompletionResult completionResult;

    @Before
    public void setUp() {
        // 设置模型属性
        ReflectionTestUtils.setField(relatedQuestionService, "model", "ERNIE_3_8K");
    }

    @Test
    public void testAskRelatedQuestions_NullRequestOrBlankQuery() {
        List<AskRelatedQuestionResponse> responses = relatedQuestionService.askRelatedQuestions("user", null);
        assertTrue(responses.isEmpty());

        responses = relatedQuestionService.askRelatedQuestions("user", new AskRelatedQuestionRequest());
        assertTrue(responses.isEmpty());
    }

    @Test
    public void testAskRelatedQuestions_NormalExecution() {
        // 模拟请求和上下文
        AskRelatedQuestionRequest request = new AskRelatedQuestionRequest();
        request.setQuery("test query");
        AutoworkContext context = new AutoworkContext();
        context.setName("contextName");
        request.setContexts(Collections.singletonList(context));

        // 模拟模型返回的结果
        String mockJsonResult = "```json\n[\"related question 1\", \"related question 2\"]```";
        when(completionResult.getContent()).thenReturn(mockJsonResult);

        // 模拟方法调用
        RelatedQuestionServiceImpl spyService = spy(relatedQuestionService);
        doReturn(completionResult).when(spyService).chatCompletionWithErnie(any(CompletionContent.class), any(ErnieRequest.class));
        doReturn(false).when(spyService).ernieCodeRequest(anyString());

        List<AskRelatedQuestionResponse> responses = spyService.askRelatedQuestions("user", request);

        // 验证结果
        assertNotNull(responses);
        assertEquals(2, responses.size());
        assertEquals("related question 1", responses.get(0).getQuery());
        assertEquals("related question 2", responses.get(1).getQuery());
    }

    @Test
    public void testAskRelatedQuestions_ExceptionHandling() {
        // 模拟请求
        AskRelatedQuestionRequest request = new AskRelatedQuestionRequest();
        request.setQuery("test query");

        // 模拟方法调用抛出异常
        RelatedQuestionServiceImpl spyService = spy(relatedQuestionService);
        doThrow(new RuntimeException("Test Exception")).when(spyService).chatCompletionWithErnie(any(CompletionContent.class), any(ErnieRequest.class));

        List<AskRelatedQuestionResponse> responses = spyService.askRelatedQuestions("user", request);

        // 验证结果
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
    }
}