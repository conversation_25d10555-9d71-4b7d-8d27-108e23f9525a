package com.baidu.autocomate.application.analyze.strategy.impl;

import com.baidu.autocomate.application.analyze.cqe.AnalyzeCommand;
import com.baidu.autocomate.application.analyze.strategy.composer.ComposerQuotaChecker;
import com.baidu.autocomate.application.analyze.strategy.composer.ComposerSecurityAnalyzer;
import com.baidu.autocomate.application.analyze.strategy.composer.ComposerWhiteListChecker;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.config.service.ModelConfigService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ComposerAnalyzeStrategyTest {

    @InjectMocks
    private ComposerAnalyzeStrategy composerAnalyzeStrategy;

    @Mock
    private AnalyzeCommand analyzeCommand;

    @Mock
    private ComposerQuotaChecker composerQuotaChecker;

    @Mock
    private ComposerSecurityAnalyzer composerSecurityAnalyzer;

    @Mock
    private ComposerWhiteListChecker composerWhiteListChecker;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ModelConfigService modelConfigService;

    @Test
    public void testAnalyzeWithoutIntent() {
        // Arrange
        when(analyzeCommand.isIntent()).thenReturn(false);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("composer_use_default_llm_only")).thenReturn("false");
        doNothing().when(composerQuotaChecker).checkAndRecordUsage(analyzeCommand);
        doNothing().when(composerSecurityAnalyzer).analyzeCodeSecurity(Mockito.any(), Mockito.any());
    
        // Act
        AnalyzeResult result = composerAnalyzeStrategy.analyze(analyzeCommand);
    
        // Assert
        assertNotNull(result);
        verify(composerSecurityAnalyzer).analyzeCodeSecurity(Mockito.any(), Mockito.any());
    }
}