package com.baidu.autocomate.application.quota.exception;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

public class ComposerUnauthorizedExceptionTest {

    @Test
    public void testDefaultConstructor() {
        // 测试无参构造函数
        ComposerUnauthorizedException exception = new ComposerUnauthorizedException();
        
        // 验证异常是RuntimeException的子类
        assertTrue(exception instanceof RuntimeException);
        
        // 验证消息和cause都为null
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testMessageConstructor() {
        // 测试带消息的构造函数
        String errorMessage = "用户不在白名单";
        ComposerUnauthorizedException exception = new ComposerUnauthorizedException(errorMessage);
        
        // 验证异常消息
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testMessageAndCauseConstructor() {
        // 测试带消息和cause的构造函数
        String errorMessage = "用户不在白名单";
        Throwable cause = new IllegalArgumentException("原始错误");
        
        ComposerUnauthorizedException exception = 
            new ComposerUnauthorizedException(errorMessage, cause);
        
        // 验证异常消息和cause
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals("原始错误", exception.getCause().getMessage());
    }

    @Test
    public void testNullMessageAndCause() {
        // 测试null消息和cause
        ComposerUnauthorizedException exception = 
            new ComposerUnauthorizedException(null, null);
        
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testCauseWithoutMessage() {
        // 测试只有cause没有消息的情况
        Throwable cause = new RuntimeException("原始错误");
        ComposerUnauthorizedException exception = 
            new ComposerUnauthorizedException(null, cause);
        
        assertNull(exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testMessageWithoutCause() {
        // 测试只有消息没有cause的情况
        String errorMessage = "测试错误";
        ComposerUnauthorizedException exception = 
            new ComposerUnauthorizedException(errorMessage, null);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testStackTracePreservation() {
        // 测试堆栈跟踪是否被保留
        try {
            throw new ComposerUnauthorizedException("测试错误");
        } catch (ComposerUnauthorizedException e) {
            StackTraceElement[] stackTrace = e.getStackTrace();
            assertNotNull(stackTrace);
            assertTrue(stackTrace.length > 0);
            // 验证第一个堆栈元素是当前测试方法
            assertEquals("testStackTracePreservation", stackTrace[0].getMethodName());
        }
    }

    @Test
    public void testCauseStackTracePreservation() {
        // 测试cause的堆栈跟踪是否被保留
        Exception originalException = new Exception("原始错误");
        ComposerUnauthorizedException exception = 
            new ComposerUnauthorizedException("包装错误", originalException);
        
        assertNotNull(exception.getCause().getStackTrace());
        assertTrue(exception.getCause().getStackTrace().length > 0);
    }
}