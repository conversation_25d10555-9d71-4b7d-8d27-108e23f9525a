package com.baidu.autocomate.application.search.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.baidu.autocomate.application.search.bean.SearchResult;
import com.baidu.autocomate.application.search.cqe.SearchCommand;
import com.baidu.autocomate.application.search.factory.SearchStrategyFactory;
import com.baidu.autocomate.application.search.strategy.SearchStrategy;
import com.baidu.autocomate.domain.analyze.bean.AnalyzeResult;
import com.baidu.autocomate.domain.knowledge.code.bean.CodeChunk;
import com.baidu.autocomate.domain.search.entity.SearchResultPersistentEntity;
import com.baidu.autocomate.domain.search.repository.SearchResultRepository;
import com.baidu.autocomate.domain.searcher.SearchType;
import com.baidu.autocomate.domain.searcher.result.DataSearchResult;
import com.baidu.autocomate.domain.searcher.searcher.graph.GraphSearchResult;

@RunWith(MockitoJUnitRunner.class)
public class SearchServiceImplTest {

    @InjectMocks
    private SearchServiceImpl searchService;

    @Mock
    private SearchResultRepository searchResultRepository;

    @Mock
    private SearchStrategy searchStrategy;

    private SearchCommand searchCommand;
    private DataSearchResult dataSearchResult;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        searchCommand = new SearchCommand();
        searchCommand.setConversationId(1L);
        searchCommand.setMessageId(1L);
        searchCommand.setSlash("智能问答-V2");
        searchCommand.setQuery("some query");
        searchCommand.setUserName("username");
        searchCommand.setAnalyze(new AnalyzeResult());

        dataSearchResult = DataSearchResult.empty();
        when(searchStrategy.search(any(SearchCommand.class))).thenReturn(dataSearchResult);
    }

    @Test
    public void testSearch() {
        try (MockedStatic<SearchStrategyFactory> mockedStatic = Mockito.mockStatic(SearchStrategyFactory.class)) {
            mockedStatic.when(() -> SearchStrategyFactory.getSearchStrategy(anyString())).thenReturn(searchStrategy);
            // Act
            SearchResult result = searchService.search(searchCommand);

            // Assert
            verify(searchStrategy, times(1)).search(searchCommand);
            verify(searchResultRepository, times(1)).save(any(SearchResultPersistentEntity.class));

            assertNotNull(result);
            assertEquals(searchCommand.getConversationId(), result.getConversationId());
            assertEquals(searchCommand.getMessageId(), result.getMessageId());
            assertTrue(result.getCodeChunks().isEmpty());
        }
    }

    @Test
    public void testSearchWithCodeChunks() {
        try (MockedStatic<SearchStrategyFactory> mockedStatic = Mockito.mockStatic(SearchStrategyFactory.class)) {
            mockedStatic.when(() -> SearchStrategyFactory.getSearchStrategy(anyString())).thenReturn(searchStrategy);
            // Arrange
            CodeChunk codeChunk = new CodeChunk();
            List<CodeChunk> codeChunks = Collections.singletonList(codeChunk);
            com.baidu.autocomate.domain.searcher.result.SearchResult<?> codeSearchResult =
                    new com.baidu.autocomate.domain.searcher.result.SearchResult(SearchType.CODE, codeChunks);
            dataSearchResult.addData(SearchType.CODE, codeSearchResult);

            // Act
            SearchResult result = searchService.search(searchCommand);

            // Assert
            assertNotNull(result);
            assertEquals(searchCommand.getConversationId(), result.getConversationId());
            assertEquals(searchCommand.getMessageId(), result.getMessageId());
            assertEquals(codeChunks, result.getCodeChunks());
        }
    }

    @Test
    public void testSearchWithGraphRender() {
        try (MockedStatic<SearchStrategyFactory> mockedStatic = Mockito.mockStatic(SearchStrategyFactory.class)) {
            mockedStatic.when(() -> SearchStrategyFactory.getSearchStrategy(anyString())).thenReturn(searchStrategy);
            // Arrange
            GraphSearchResult graphSearchResult = new GraphSearchResult();
            graphSearchResult.setGraphRenderData(new Object());
            List<GraphSearchResult> graphSearchResults = Collections.singletonList(graphSearchResult);
            dataSearchResult.addData(SearchType.CODE_GRAPH, new com.baidu.autocomate.domain.searcher.result.SearchResult(SearchType.CODE_GRAPH, graphSearchResults));

            // Act
            SearchResult result = searchService.search(searchCommand);

            // Assert
            assertNotNull(result);
        }
    }
}