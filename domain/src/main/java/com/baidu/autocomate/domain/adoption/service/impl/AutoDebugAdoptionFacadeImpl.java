package com.baidu.autocomate.domain.adoption.service.impl;

import com.baidu.autocomate.domain.adoption.bean.AutoDebugAdoptionResponse;
import com.baidu.autocomate.domain.adoption.bean.AutoDebugGenerationResponse;
import com.baidu.autocomate.domain.adoption.cqe.AutoDebugGenerationCommand;
import com.baidu.autocomate.domain.adoption.service.AutoDebugAdoptionFacade;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

@Slf4j
@Service
public class AutoDebugAdoptionFacadeImpl implements AutoDebugAdoptionFacade {

    @Value("${autodDebug.generation.url}")
    private String generationUrl;

    @Value("${autodDebug.adoption.url}")
    private String adoptionUrl;

    @Override
    public void uploadGeneration(AutoDebugGenerationCommand command) {
        if (command == null) {
            return;
        }

        String bodyString = GSON.toJson(command);
        Request request = new Request.Builder().url(generationUrl)
                .post(RequestBody.create(bodyString, HttpConstants.JSON)).build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("upload autoDebug generation error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.info("upload autoDebug generate response: {}", responseJson);
            AutoDebugGenerationResponse autoDebugGenerateResponse = GsonConstants.GSON.fromJson(responseJson, AutoDebugGenerationResponse.class);
            if (HttpStatus.SC_OK != autoDebugGenerateResponse.getCode()) {
                log.error("upload autoDebug generation error, response:{}", responseJson);
            }
        } catch (Exception e) {
            log.error("upload autoDebug error", e);
        }
    }

    @Override
    public void uploadAdoption(String adoptionUuid) {
        if (StringUtils.isBlank(adoptionUuid)) {
            return;
        }
        HttpUrl httpUrl = HttpUrl.parse(adoptionUrl).newBuilder().addQueryParameter("uuid", adoptionUuid).build();
        Request request = new Request.Builder().url(httpUrl).post(RequestBody.create(null, new byte[0])).build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("upload autoDebug adoption error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.error("upload autoDebug adoption error, response:{}", response);
            AutoDebugAdoptionResponse autoDebugAdoptionResponse = GsonConstants.GSON.fromJson(responseJson,
                    AutoDebugAdoptionResponse.class);
            if (HttpStatus.SC_OK != autoDebugAdoptionResponse.getCode()) {
                log.error("upload autoDebug adoption error, response:{}", responseJson);
            }
        } catch (Exception e) {
            log.error("upload autoDebug error", e);
        }
    }
}
