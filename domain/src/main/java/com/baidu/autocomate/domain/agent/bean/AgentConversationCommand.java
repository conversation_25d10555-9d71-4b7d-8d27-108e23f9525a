package com.baidu.autocomate.domain.agent.bean;

import com.baidu.autocomate.domain.agent.enums.AgentInitType;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import com.baidu.autocomate.domain.message.enums.MessageContentSource;
import com.baidu.autocomate.domain.message.enums.MessageInteractionMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * agent对话指令
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentConversationCommand {

    /**
     * agent id
     */
    private Long agentId;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 对话内容
     */
    private MessageContentEntity content;

    /**
     * workflow build id
     */
    private Long workflowBuildId;

    /**
     * 任务 build id
     */
    private Long jobBuildId;

    /**
     * 用户
     */
    private String username;

    /**
     * 指定意图
     */
    private String slash;


    /**
     * 关联的知识id，字符串逗号拼接，"1,2,3"
     */
    private String knowledge;

    private String workspace;
    
    private AgentInitType initType;

    private Long messageTaskId;
    
    private MessageContentSource contentSource = MessageContentSource.MYSQL;
    
    public void validate() {
        Assert.notNull(content, "content is null");
        // Assert.notNull(workflowBuildId, "workflowBuildId is null");
        Assert.notNull(agentId, "agentId is null");
        Assert.notNull(conversationId, "conversationId is null");
        Assert.notNull(username, "username is null");
    }

    public String getSenderSessionId() {
        return null == this.content ? null : this.content.getSenderSessionId();
    }
    
    public String getEmitterId() {
        return null == this.content ? null : this.content.getEmitterId();
    }
    
    public MessageInteractionMode getInteractionMode() {
        return null == this.content ? MessageInteractionMode.WEBSOCKET : this.content.getInteractionMode();
    }
}
