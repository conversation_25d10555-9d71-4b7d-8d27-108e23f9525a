package com.baidu.autocomate.domain.agent.bean;

import com.baidu.autocomate.domain.agent.enums.AgentInitType;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

@Data
@Builder
public class AgentInitCommand {

    private @NotNull AgentInitType type;

    private @NotNull Long agentId;

    private @NotNull String workspace;

    private @NotNull String knowledge;

    private @NotNull String username;

    /**
     * 表示请求来源，如：web、cli等
     */
    private String source = StringUtils.EMPTY;
}
