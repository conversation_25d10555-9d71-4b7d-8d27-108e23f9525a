package com.baidu.autocomate.domain.agent.bean;

import com.baidu.autocomate.domain.agent.enums.AgentInitType;
import com.baidu.autocomate.domain.message.entity.MessageContentEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 快速发起会话，初始化和用户Query在一起
 */
@Data
public class AgentQuickConversationCommand {

    private @NotNull AgentInitType type;

    private @NotNull Long agentId;

    private @NotNull String workspace;

    private @NotNull String knowledge;

    private @NotNull String username;

    private @NotNull MessageContentEntity content;

    private String slash;
}
