/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.domain.adoption.service.impl;

import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baidu.autocomate.domain.adoption.bean.TestMateCodeAdoptResult;
import com.baidu.autocomate.domain.adoption.cqe.CodeAdoptionUploadCommand;
import com.baidu.autocomate.domain.adoption.cqe.TestMateUploadCommand;
import com.baidu.autocomate.domain.adoption.service.TestMateCodeUpload;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Service
@Slf4j
public class TestMateCodeUploadImpl implements TestMateCodeUpload {
    @Value("${testmate.generate.url}")
    private String generateUrl;

    @Value("${external:false}")
    private boolean external;

    /**
     * 异步上传生成的代码到testmate，不阻塞主流程
     * @param command
     * @param codeAdoptionStringUuid
     */
    @Override
    @Async
    public void uploadGenerated(CodeAdoptionUploadCommand command, String codeAdoptionStringUuid) {

        TestMateUploadCommand testMateUploadCommand = TestMateUploadCommand.builder()
                .source("AutoWork")
                .generateContent(command.getOriginGeneratedContent())
                .scene("intelli_test")
                .platform("AutoWork")
                .generateRecordUuid(codeAdoptionStringUuid)
                .build();

        // saas版本用户名的处理
        String username = external ? "AutoWork" : command.getUsername();
        if (external) {
            testMateUploadCommand.setPlatformUuid(command.getUsername());
        }

        // 调用testmate生成接口
        Request httpRequest = new Request.Builder()
                .url(generateUrl)
                .addHeader("x-baidu-int-username", username)
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(testMateUploadCommand)))
                .build();

        try (Response response = HttpConstants.codeSearchClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("upload testmate error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.info("upload testmate generate response: {}", responseJson);
            TestMateCodeAdoptResult
                    codeAdoptResult = GsonConstants.GSON.fromJson(responseJson, TestMateCodeAdoptResult.class);
            if (HttpStatus.SC_OK != codeAdoptResult.getCode()) {
                log.error("upload testmate error, response:{}", responseJson);
            }

        } catch (Exception e) {
            log.error("upload testmate error", e);
        }

    }

}
