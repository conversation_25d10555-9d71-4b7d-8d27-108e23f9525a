package com.baidu.autocomate.domain.account.repos;

import com.baidu.autocomate.domain.account.entity.WebAccountEntity;
import com.baidu.autocomate.domain.account.enums.UserAccountType;

import java.time.LocalDateTime;

public interface AccountRepository {

    /**
     * 获取 userAccountType 为UNlogin的，并且没有被绑上任何licence的
     * @param uniqueIdentifier
     * @return
     */
    WebAccountEntity getValidUnLoginUserByIdentifier(String uniqueIdentifier);

    WebAccountEntity findByIdentifierAndType(String uniqueIdentifier, UserAccountType userAccountType);

    WebAccountEntity createUnLoginAccount();

    UserAccountType getUserAccountType(String uniqueIdentifier);

    void bindUnLoginAccountToLoginAccount(String unLoginUniqueIdentifier, String loginUniqueIdentifier, String source);

    Integer queryNewRegisterNum(LocalDateTime startDate, LocalDateTime endDate);

    Integer unLoginChatUserNum(LocalDateTime startDate, LocalDateTime endDate);
}