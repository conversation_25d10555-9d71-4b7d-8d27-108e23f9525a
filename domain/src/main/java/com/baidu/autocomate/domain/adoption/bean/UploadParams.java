package com.baidu.autocomate.domain.adoption.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UploadParams {
    /**
     * 本次请求的唯一ID，通过UUID生成
     */
    private String uuid;
    private Long conversationId;
    private Long messageId;
    /**
     * 厂内是username，厂外是license
     */
    /**
     * 代码库ID，由插件端计算
     */
    private String repoId;
    /**
     * 代码库名称，由插件端获取
     * 比如： baidu/icafe/luigi
     */
    private String repoName;
    /**
     * 当前文件的绝对路径
     */
    private String currentFilePath;
    /**
     * 用户输入的内容
     */
    private String userInput;
    /**
     * 厂内是username，厂外是license
     */
    private String loginName;
    /**
     * 用户显示名
     */
    private String username;
    private String license;
    private String device;
    /**
     * 上报数据对应的功能，比如：问答、添加日志
     */
    private String function;
    /**
     * 模型会话的ID
     */
    private String chatId;
    /**
     * 模型生成的内容
     */
    private String generatedContent;
    /**
     * 请求模型的Prompt
     */
    private String prompt;
    /**
     * 给模型传的上下文，不包含Prompt
     * 比如代码、知识等
     */
    private String code;
    /**
     * 基础信息
     */
    private UploadBaseInfo uploadBaseInfo;
    /**
     * 模型名称
     */
    private String model;
    /**
     * 请求开始时间
     */
    private Date startTime;
    /**
     * 请求结束时间
     */
    private Date endTime;
    /**
     * 首包的时间
     */
    private Date firstPackTime;
    /**
     * 输入token
     */
    private int inputTokenCount;
    /**
     * 输出token
     */
    private int outputTokenCount;
}
