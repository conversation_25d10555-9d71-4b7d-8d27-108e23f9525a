package com.baidu.autocomate.domain.adoption.strategy;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class UploadStrategyFactory {
    @Autowired
    private ApplicationContext context;

    public UploadStrategy getStrategy(String strategy) {
        if (StringUtils.isBlank(strategy)) {
            throw new RuntimeException("upload strategy name 不能为空");
        }
        try {
            return context.getBean(strategy, UploadStrategy.class);
        } catch (Exception e) {
            throw new RuntimeException("找不到对应的策略: " + strategy);
        }
    }
}
