/*
 * Copyright (C) 2024 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.autocomate.domain.adoption.service.impl;

import java.util.Objects;

import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baidu.autocomate.domain.adoption.bean.TestMateCodeAdoptResult;
import com.baidu.autocomate.domain.adoption.cqe.CodeAdoptionUploadCommand;
import com.baidu.autocomate.domain.adoption.cqe.IApiUploadCommand;
import com.baidu.autocomate.domain.adoption.service.IApiCodeUpload;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;

import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Service
@Slf4j
public class IApiCodeUploadImpl implements IApiCodeUpload {
    @Value("${iapi.adopt.url}")
    private String generateUrl;

    @Value("${external:false}")
    private boolean external;

    /**
     * 异步上传生成的代码到iapi，不阻塞主流程
     * @param command
     * @param codeAdoptionStringUuid
     * @param eventCode
     */
    @Override
    @Async
    public void uploadGenerated(CodeAdoptionUploadCommand command, String codeAdoptionStringUuid, Integer eventCode) {


        HttpUrl httpUrl = Objects.requireNonNull(HttpUrl.parse(generateUrl)).newBuilder()
                .addQueryParameter("username", command.getUsername())
                .addQueryParameter("platform","comate")
                .addQueryParameter("uuid", codeAdoptionStringUuid)
                .build();

        IApiUploadCommand iApiUploadCommand = IApiUploadCommand.builder()
                .generateContent(command.getOriginGeneratedContent())
                .eventCode(eventCode)
                .external(external)
                .build();

        // 调用iapi生成接口
        Request httpRequest = new Request.Builder()
                .url(httpUrl)
                .post(RequestBody.create(HttpConstants.JSON, GsonConstants.GSON.toJson(iApiUploadCommand)))
                .build();

        try (Response response = HttpConstants.codeSearchClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("upload iapi error, response:{}", response);
            }
            String responseJson = response.body().string();
            log.info("upload iapi generate response: {}", responseJson);
            TestMateCodeAdoptResult
                    codeAdoptResult = GsonConstants.GSON.fromJson(responseJson, TestMateCodeAdoptResult.class);
            if (HttpStatus.SC_OK != codeAdoptResult.getCode()) {
                log.error("upload iapi error, response:{}", responseJson);
            }

        } catch (Exception e) {
            log.error("upload iapi error", e);
        }

    }
}
