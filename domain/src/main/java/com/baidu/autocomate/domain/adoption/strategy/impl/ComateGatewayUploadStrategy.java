package com.baidu.autocomate.domain.adoption.strategy.impl;

import com.baidu.autocomate.domain.adoption.bean.UploadBaseInfo;
import com.baidu.autocomate.domain.adoption.bean.UploadParams;
import com.baidu.autocomate.domain.adoption.bean.UploadResult;
import com.baidu.autocomate.domain.adoption.cqe.CodeAdoptionUploadCommand;
import com.baidu.autocomate.domain.adoption.service.ComateCodeAdoptionFacade;
import com.baidu.autocomate.domain.adoption.strategy.UploadStrategy;
import com.baidu.autocomate.domain.common.ide.IDEType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.baidu.autocomate.domain.adoption.constant.CodeAdoptConstants.AUTOWORK_MODEL;

/**
 * 上报到comate gateway
 */
@Component("defaultUploadStrategy")
public class ComateGatewayUploadStrategy implements UploadStrategy {
    @Autowired
    private ComateCodeAdoptionFacade comateCodeAdoptionFacade;

    @Override
    public UploadResult uploadAfter(UploadParams params) {
        UploadBaseInfo uploadBaseInfo = params.getUploadBaseInfo() == null ? new UploadBaseInfo() : params.getUploadBaseInfo();
        CodeAdoptionUploadCommand command = CodeAdoptionUploadCommand.builder()
                .uuid(params.getUuid())
                .key(params.getLoginName())
                .ide(StringUtils.isNotBlank(uploadBaseInfo.getIdeName()) ?
                        uploadBaseInfo.getIdeName() : IDEType.VSCODE.name().toLowerCase())
                .repo(params.getRepoName())
                .path(params.getCurrentFilePath())
                .userInput(params.getUserInput())
                .username(params.getLoginName())
                .model(AUTOWORK_MODEL)
                .function(params.getFunction())
                .chatId(params.getChatId())
                .content(params.getCode())
                .originGeneratedContent(params.getGeneratedContent())
                .generatedContent(params.getGeneratedContent())
                .segmentedContent(params.getPrompt())
                .device(params.getDevice())
                .ideVersion(uploadBaseInfo.getIdeVersion())
                .pluginVersion(uploadBaseInfo.getExtVersion())
                .build();
        comateCodeAdoptionFacade.uploadGenerated(command);
        return null;
    }
}
