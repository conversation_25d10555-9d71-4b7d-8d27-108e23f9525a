package com.baidu.autocomate.domain.adoption.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@SuperBuilder
public class XiaoMiUploadRequest extends UploadBaseInfo {
    private String eventCode;
    private String requestId;
    private String conversationId;
    private String messageId;
    private String function;
    private long triggerAt;
    private long cost;
    private long firstPackCost;
    private String result;
    private int inputTokenCount;
    private int outputTokenCount;
    private long editAt;
    private String modelId;
    /**
     * 输入模型内容
     * 不包含system prompt
     */
    private String extra;
    /**
     * 模型输出内容
     */
    private String objects;
}
