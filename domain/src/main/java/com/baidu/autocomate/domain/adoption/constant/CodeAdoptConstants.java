package com.baidu.autocomate.domain.adoption.constant;

public interface CodeAdoptConstants {
    String AUTOWORK_MODEL = "Autowork";

    /**
     * 智能粘贴
     */
    String SMART_PASTE_FUNCTION = "SmartPaste";

    /**
     * 智能采纳
     */
    String AUTO_APPLY_FUNCTION = "AutoApply";

    /**
     * 侧边栏问答
     */
    String CODE_QA_FUNCTION = "QA";

    /**
     * 问答无上下文
     */
    String CODE_QA_NO_CONTEXT_FUNCTION = "QA_NO_CONTEXT";
}
