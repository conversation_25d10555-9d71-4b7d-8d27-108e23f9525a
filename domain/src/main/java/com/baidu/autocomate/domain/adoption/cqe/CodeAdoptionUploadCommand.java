package com.baidu.autocomate.domain.adoption.cqe;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CodeAdoptionUploadCommand {
    private String username;
    private String repo;
    @Builder.Default
    private int row = 1;
    @Builder.Default
    private int col = 1;
    /**
     * saas license
     */
    @Builder.Default
    private String key = "";
    @Builder.Default
    private String path = ".java";
    /**
     * 写死Autowork
     */
    @Builder.Default
    private String model = "Autowork";
    /**
     * Autowork的不同功能可以区分开
     * 传入的是Autowork的指令(Slash)，比如：智能问答
     */
    @Builder.Default
    private String function = "QA";
    @Builder.Default
    private boolean multiline = true;
    @Builder.Default
    private String userInputHistory = "None";

    /**
     * 用户输入的问题
     */
    private String userInput;
    /**
     * 用户选中的代码片段
     */
    @Builder.Default
    private String content = "None";
    /**
     * 模型生成的代码
     * 仅包含代码，不包含文本
     */
    private String originGeneratedContent;

    /**
     * 记录采纳的内容，按照wangning42那边的逻辑，上报时需要跟originGeneratedContent一致
     * 当用户复制采纳时，这个内容会被修改为用户复制的代码，可以用于计算采纳行
     */
    private String generatedContent;

    /**
     * 客户端：vscode、intellij
     */
    @Builder.Default
    private String ide = "intellij";

    /**
     * ide版本
     */
    private String ideVersion;

    /**
     * 插件版本
     */
    private String pluginVersion;

    private String device;

    /**
     * 请求一言后返回的会话ID
     * 例如：as-iq4jzenfnf
     */
    private String chatId;

    private String uuid;

    /**
     * 给模型的完整的prompt
     */
    private String segmentedContent;

    /**
     * 是否展示，默认为true，表示已展示
     */
    @Builder.Default
    private boolean shown = true;
}
