package com.baidu.autocomate.domain.adoption.strategy;

import com.baidu.autocomate.domain.adoption.bean.UploadParams;
import com.baidu.autocomate.domain.adoption.bean.UploadResult;

public interface UploadStrategy {
    /**
     * 请求之前上报
     *
     * @param params 上报参数
     * @return 上报结果
     */
    default UploadResult uploadBefore(UploadParams params) {
        return null;
    }

    /**
     * 请求之后上报
     *
     * @param params 上报参数
     * @return 上报结果
     */
    UploadResult uploadAfter(UploadParams params);
}
