package com.baidu.autocomate.domain.adoption.service;

import com.baidu.autocomate.domain.adoption.bean.CodeAdoptionFunction;
import com.baidu.autocomate.domain.adoption.bean.CodeAdoptionResult;
import com.baidu.autocomate.domain.adoption.bean.ModelMetricsResult;
import com.baidu.autocomate.domain.adoption.cqe.CodeAdoptionUploadCommand;
import com.baidu.autocomate.domain.adoption.cqe.ModelMetricsUploadCommand;
import com.baidu.autocomate.domain.common.UserDeviceInfo;
import lombok.NonNull;
import org.apache.commons.lang3.ObjectUtils;

public interface ComateCodeAdoptionFacade {

    /**
     * 上传代码推荐数据，到统计平台
     *
     * @param userDeviceInfo  用户设备信息
     * @param generateContent 模型生成的内容
     * @param repo            代码仓库
     * @param currentFilePath
     * @param originCode      原始代码
     * @return 统计平台返回的uuid
     */
    default String uploadCodeAdoption(UserDeviceInfo userDeviceInfo, String generateContent, String repo,
                                      String currentFilePath, String originCode,
                                      @NonNull CodeAdoptionFunction function, String uuid) {
        // 数据上报，生成uuid
        CodeAdoptionUploadCommand command = CodeAdoptionUploadCommand.builder()
                .key(userDeviceInfo.getUsername())
                .ide(userDeviceInfo.getIde())
                .repo(repo)
                .path(currentFilePath)
                .userInput(originCode)
                .username(userDeviceInfo.getUsername())
                .function(function.name())
                .originGeneratedContent(generateContent)
                .generatedContent(generateContent)
                .device(userDeviceInfo.getDevice())
                .uuid(uuid)
                .build();
        CodeAdoptionResult codeAdoptionResult = uploadGenerated(command);
        return ObjectUtils.allNotNull(codeAdoptionResult, codeAdoptionResult.getData()) ?
                codeAdoptionResult.getData().getUuid() : null;
    }

    default String uploadCodeGenerated(CodeAdoptionUploadCommand command) {
        CodeAdoptionResult codeAdoptionResult = uploadGenerated(command);
        return ObjectUtils.allNotNull(codeAdoptionResult, codeAdoptionResult.getData()) ?
                codeAdoptionResult.getData().getUuid() : null;
    }

    /**
     * 上报生成的代码
     *
     * @param command
     * @return
     */
    CodeAdoptionResult uploadGenerated(CodeAdoptionUploadCommand command);

    /**
     * SAAS 上报千帆模型指标数据到BAP
     *
     * @param command ModelMetricsUploadCommand
     * @return
     */
    ModelMetricsResult uploadModelMetrics(ModelMetricsUploadCommand command);
}
