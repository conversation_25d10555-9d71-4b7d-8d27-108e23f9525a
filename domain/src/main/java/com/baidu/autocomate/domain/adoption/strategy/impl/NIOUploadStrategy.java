package com.baidu.autocomate.domain.adoption.strategy.impl;

import com.baidu.autocomate.domain.adoption.bean.UploadBaseInfo;
import com.baidu.autocomate.domain.adoption.bean.UploadParams;
import com.baidu.autocomate.domain.adoption.bean.UploadResult;
import com.baidu.autocomate.domain.adoption.bean.XiaoMiUploadRequest;
import com.baidu.autocomate.domain.adoption.strategy.UploadStrategy;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component("nioUploadStrategy")
@Slf4j
public class NIOUploadStrategy implements UploadStrategy {
    private static final String TRIGGER_EVENT_CODE = "comate_chat_trigger";
    private static final String FINISH_EVENT_CODE = "comate_chat_performance";
    private static final String RESULT_SUCCESS = "success";

    @Value("${upload.nio.url:http://180.76.151.153/audit/log}")
    private String url;

    @Override
    public UploadResult uploadBefore(UploadParams params) {
        if (params == null) {
            return null;
        }
        UploadBaseInfo baseInfo = params.getUploadBaseInfo();
        if (baseInfo == null) {
            baseInfo = UploadBaseInfo.builder().build();
        }

        long startTime = params.getStartTime() != null ? params.getStartTime().getTime() : System.currentTimeMillis();
        XiaoMiUploadRequest miUploadRequest = XiaoMiUploadRequest.builder()
                .eventCode(TRIGGER_EVENT_CODE)
                .os(baseInfo.getOs()).osVersion(baseInfo.getOsVersion())
                .extName(baseInfo.getExtName()).extVersion(baseInfo.getExtVersion())
                .ideType(baseInfo.getIdeType()).ideName(baseInfo.getIdeName()).ideVersion(baseInfo.getIdeVersion())
                .vcsType(baseInfo.getVcsType()).vcsRepo(baseInfo.getVcsRepo())
                .vcsBranchName(baseInfo.getVcsBranchName())
                .username(params.getLoginName())
                .requestId(params.getUuid())
                .conversationId("" + params.getConversationId())
                .messageId("" + params.getMessageId())
                .function(params.getFunction())
                .triggerAt(startTime)
                .inputTokenCount(params.getInputTokenCount())
                .modelId(params.getModel())
                .objects(params.getCode())
                .build();

        String bodyJson = GsonConstants.GSON.toJson(miUploadRequest);
        log.info("nioUploadStrategy.uploadBefore request: {}", bodyJson);
        RequestBody requestBody = RequestBody.create(bodyJson, HttpConstants.JSON);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            log.info("nioUploadStrategy.uploadBefore response: {}", response.body().string());
        } catch (IOException e) {
            log.error("nioUploadStrategy.uploadBefore error: {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public UploadResult uploadAfter(UploadParams params) {
        if (params == null) {
            return null;
        }
        UploadBaseInfo baseInfo = params.getUploadBaseInfo();
        if (baseInfo == null) {
            baseInfo = UploadBaseInfo.builder().build();
        }

        long startTime = params.getStartTime() != null ? params.getStartTime().getTime() : System.currentTimeMillis();
        long endTime = params.getEndTime() != null ? params.getEndTime().getTime() : System.currentTimeMillis();
        long firstPackTime = params.getFirstPackTime() != null ? params.getFirstPackTime().getTime() : startTime;
        XiaoMiUploadRequest miUploadRequest = XiaoMiUploadRequest.builder()
                .eventCode(FINISH_EVENT_CODE).os(baseInfo.getOs()).osVersion(baseInfo.getOsVersion())
                .extName(baseInfo.getExtName()).extVersion(baseInfo.getExtVersion())
                .ideName(baseInfo.getIdeName()).ideVersion(baseInfo.getIdeVersion())
                .vcsType(baseInfo.getVcsType()).vcsRepo(baseInfo.getVcsRepo())
                .vcsBranchName(baseInfo.getVcsBranchName())
                .username(params.getLoginName())
                .requestId(params.getUuid())
                .conversationId("" + params.getConversationId())
                .messageId("" + params.getMessageId())
                .function(params.getFunction())
                .triggerAt(startTime)
                .cost(endTime - startTime)
                .firstPackCost(firstPackTime - startTime)
                .result(RESULT_SUCCESS)
                .inputTokenCount(params.getInputTokenCount())
                .outputTokenCount(params.getOutputTokenCount())
                .editAt(endTime)
                .modelId(params.getModel())
                .objects(params.getCode())
                .extra(params.getGeneratedContent())
                .build();

        String bodyJson = GsonConstants.GSON.toJson(miUploadRequest);
        log.info("nioUploadStrategy.upload request: {}", bodyJson);
        RequestBody requestBody = RequestBody.create(bodyJson, HttpConstants.JSON);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            log.info("nioUploadStrategy.upload response: {}", response.body().string());
        } catch (IOException e) {
            log.error("nioUploadStrategy.upload error: {}", e.getMessage(), e);
        }
        return null;
    }
}
