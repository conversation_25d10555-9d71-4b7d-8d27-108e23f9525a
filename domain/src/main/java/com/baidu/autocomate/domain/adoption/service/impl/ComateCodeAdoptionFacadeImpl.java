package com.baidu.autocomate.domain.adoption.service.impl;

import com.baidu.autocomate.domain.adoption.bean.CodeAdoptionResult;
import com.baidu.autocomate.domain.adoption.bean.ModelMetricsResult;
import com.baidu.autocomate.domain.adoption.cqe.CodeAdoptionUploadCommand;
import com.baidu.autocomate.domain.adoption.cqe.ModelMetricsUploadCommand;
import com.baidu.autocomate.domain.adoption.service.ComateCodeAdoptionFacade;
import com.baidu.autocomate.domain.common.HttpConstants;
import com.baidu.autocomate.domain.llm.model.ernie.bean.ErnieModelType;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.baidu.autocomate.domain.common.GsonConstants.GSON;

@Slf4j
@Service
public class ComateCodeAdoptionFacadeImpl implements ComateCodeAdoptionFacade {

    @Value("${comate.adoption.url:http://10.11.58.93:8080/generate}")
    private String adoptionUrl;

    @Value("${model.metrics.url:http://10.11.144.132:8030/metrics/report/model/usages}")
    private String modelMetricsUrl;
    
    @Value("${external:false}")
    private boolean external;

    @Value("${ernie.enable.metrics:false}")
    private boolean metricsEnable;

    @Override
    public CodeAdoptionResult uploadGenerated(CodeAdoptionUploadCommand command) {
        if (command == null) {
            return null;
        }

        log.info("upload generated code request: {}", GSON.toJson(command));
        Response response = null;
        try {
            FormBody.Builder formBodyBuilder = new FormBody.Builder()
                    .add("username", StringUtils.isBlank(command.getUsername()) ? "" : command.getUsername())
                    .add("repo", StringUtils.isBlank(command.getRepo()) ? "" : command.getRepo())
                    // content不能为空字符串
                    .add("content", StringUtils.isBlank(command.getContent()) ? "None" : command.getContent())
                    .add("row", String.valueOf(command.getRow()))
                    .add("col", String.valueOf(command.getCol()))
                    // SaaS版才传key
                    .add("key", external ? command.getKey() : "")
                    .add("ide", command.getIde())
                    // path不能为空字符串
                    .add("path", StringUtils.isBlank(command.getPath()) ? ".java" : command.getPath())
                    .add("model", command.getModel())
                    .add("function", command.getFunction())
                    .add("chatId", StringUtils.isBlank(command.getChatId()) ? "" : command.getChatId())
                    .add("multiline", String.valueOf(command.isMultiline()))
                    .add("userInput", StringUtils.isBlank(command.getUserInput()) ? "" : command.getUserInput())
                    .add("userInputHistory", command.getUserInputHistory())
                    .add("originGeneratedContent", StringUtils.isBlank(command.getOriginGeneratedContent())
                            ? "" : command.getOriginGeneratedContent())
                    .add("generatedContent", StringUtils.isBlank(command.getGeneratedContent())
                            ? "" : command.getGeneratedContent())
                    .add("shown", String.valueOf(command.isShown()))
                    .add("device", external ? (command.getDevice() == null ? "default" : command.getDevice()) : "")
                    .add("ideVersion", command.getIdeVersion() == null ? "" : command.getIdeVersion())
                    .add("pluginVersion", command.getPluginVersion() == null ? "" : command.getPluginVersion())
                    .add("segmentedContent", command.getSegmentedContent() == null ? ""
                            : command.getSegmentedContent());
            if (StringUtils.isNotBlank(command.getUuid())) {
                formBodyBuilder.add("uuid", command.getUuid());
            }
            RequestBody formBody = formBodyBuilder.build();
            Request.Builder builder = new Request.Builder()
                    .url(adoptionUrl)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .post(formBody);
            if (external) {
                builder.addHeader(HttpConstants.X_COMATE_LICENSE_HEADER, command.getUsername());
            }
            Request request = builder.build();
            response = HttpConstants.codeSearchClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("upload generated code error, status: {}", response.code());
                return null;
            }
            String responseJson = response.body().string();
            log.info("upload generated code response: {}", responseJson);
            CodeAdoptionResult result = GSON.fromJson(responseJson, CodeAdoptionResult.class);
            return result;
        } catch (Exception e) {
            log.error("upload generated code error", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    @Override
    public ModelMetricsResult uploadModelMetrics(ModelMetricsUploadCommand command) {
        if (command == null || !external || !metricsEnable) {
            return null;
        }

        // 仅针对SAAS的EB模型上报数据到BAP
        ErnieModelType ernieModelType = ErnieModelType.getType(command.getModelVersion());
        if (ernieModelType == null) {
            log.info("invalid model version: {}", command.getModelVersion());
            return null;
        }

        command.setModel(ernieModelType.getModel());
        command.setModelVersion(ernieModelType.getModelVersion());
        log.info("upload model metrics request: {}", GSON.toJson(command));
        Response response = null;

        try {
            Request.Builder builder = new Request.Builder()
                    .url(modelMetricsUrl)
                    .addHeader("Content-Type", "application/json")
                    .post(RequestBody.create(HttpConstants.JSON, GSON.toJson(command)));
            Request request = builder.build();
            response = HttpConstants.codeSearchClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("upload model metrics error, status: {}", response.code());
                return null;
            }
            String responseJson = response.body().string();
            log.info("upload model metrics response: {}", responseJson);
            return GSON.fromJson(responseJson, ModelMetricsResult.class);
        } catch (Exception e) {
            log.error("upload model metrics  error", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }
}
