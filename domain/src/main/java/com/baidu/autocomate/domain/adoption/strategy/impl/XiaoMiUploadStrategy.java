package com.baidu.autocomate.domain.adoption.strategy.impl;

import com.baidu.autocomate.domain.adoption.bean.UploadBaseInfo;
import com.baidu.autocomate.domain.adoption.bean.UploadParams;
import com.baidu.autocomate.domain.adoption.bean.UploadResult;
import com.baidu.autocomate.domain.adoption.bean.XiaoMiUploadRequest;
import com.baidu.autocomate.domain.adoption.strategy.UploadStrategy;
import com.baidu.autocomate.domain.common.GsonConstants;
import com.baidu.autocomate.domain.common.HttpConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component("xiaomiUploadStrategy")
@Slf4j
public class XiaoMiUploadStrategy implements UploadStrategy {
    private static final String EVENT_CODE = "copilot_chat_performance";
    private static final String RESULT_SUCCESS = "success";

    @Value("${upload.xiaomi.url:http://staging-tracking.llm.pt.mioffice.cn/tracking/comate/v1/upload}")
    private String url;

    @Override
    public UploadResult uploadAfter(UploadParams params) {
        if (params == null) {
            return null;
        }
        UploadBaseInfo baseInfo = params.getUploadBaseInfo();
        if (baseInfo == null) {
            baseInfo = UploadBaseInfo.builder().build();
        }

        long startTime = params.getStartTime() != null ? params.getStartTime().getTime() : System.currentTimeMillis();
        long endTime = params.getEndTime() != null ? params.getEndTime().getTime() : System.currentTimeMillis();
        long firstPackTime = params.getFirstPackTime() != null ? params.getFirstPackTime().getTime() : startTime;
        XiaoMiUploadRequest miUploadRequest = XiaoMiUploadRequest.builder()
                .eventCode(EVENT_CODE).os(baseInfo.getOs()).osVersion(baseInfo.getOsVersion())
                .extName(baseInfo.getExtName()).extVersion(baseInfo.getExtVersion())
                .ideType(baseInfo.getIdeType()).ideName(baseInfo.getIdeName()).ideVersion(baseInfo.getIdeVersion())
                .vcsType(baseInfo.getVcsType()).vcsRepo(baseInfo.getVcsRepo())
                .vcsBranchName(baseInfo.getVcsBranchName())
                .username(baseInfo.getUsername())
                .requestId(params.getUuid())
                .conversationId("" + params.getConversationId())
                .messageId(params.getUuid())
                .triggerAt(startTime)
                .cost(endTime - startTime)
                .firstPackCost(firstPackTime - startTime)
                .result(RESULT_SUCCESS)
                .inputTokenCount(params.getInputTokenCount())
                .outputTokenCount(params.getOutputTokenCount())
                .editAt(endTime)
                .modelId("ERNIE_BOT")
                .build();

        String bodyJson = GsonConstants.GSON.toJson(miUploadRequest);
        log.info("XiaoMiUploadStrategy.upload: {}", bodyJson);
        RequestBody requestBody = RequestBody.create(bodyJson, HttpConstants.JSON);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        try (Response response = HttpConstants.codeSearchClient.newCall(request).execute()) {
            log.info("XiaoMiUploadStrategy.upload: {}", response.body().string());
        } catch (IOException e) {
            log.error("XiaoMiUploadStrategy.upload: {}", e.getMessage(), e);
        }
        return null;
    }

}
